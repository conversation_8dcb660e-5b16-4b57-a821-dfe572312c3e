import { z } from 'zod';

export const createBankPaymentSchema = z.object({
  bankName: z.string()
    .trim()
    .min(2, 'Bank name must be at least 2 characters long')
    .max(100, 'Bank name cannot exceed 100 characters')
    .nonempty('Bank name is required'),

  accountNumber: z.string()
    .trim()
    .regex(/^[0-9]{9,18}$/, 'Account number must be 9-18 digits')
    .nonempty('Account number is required'),

  reAccountNumber: z.string()
    .trim()
    .nonempty('Please re-enter account number'),

  ifscCode: z.string()
    .trim()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code format (e.g., SBIN0001234)')
    .nonempty('IFSC code is required'),

  accountHolderName: z.string()
    .trim()
    .min(2, 'Account holder name must be at least 2 characters long')
    .max(100, 'Account holder name cannot exceed 100 characters')
    .regex(/^[A-Za-z\s]+$/, 'Account holder name can only contain letters and spaces')
    .nonempty('Account holder name is required'),

  branchName: z.string()
    .trim()
    .min(2, 'Branch name must be at least 2 characters long')
    .max(100, 'Branch name cannot exceed 100 characters')
    .nonempty('Branch name is required')
}).refine((data) => data.accountNumber === data.reAccountNumber, {
  message: 'Account numbers do not match',
  path: ['reAccountNumber']
});

export const updateBankPaymentSchema = createBankPaymentSchema;
