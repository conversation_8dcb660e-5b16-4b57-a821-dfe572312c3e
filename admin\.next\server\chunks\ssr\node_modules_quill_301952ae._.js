module.exports = {

"[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
class Break extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] {
    static value() {
        return undefined;
    }
    optimize() {
        if (this.prev || this.next) {
            this.remove();
        }
    }
    length() {
        return 0;
    }
    value() {
        return '';
    }
}
Break.blotName = 'break';
Break.tagName = 'BR';
const __TURBOPACK__default__export__ = Break;
 //# sourceMappingURL=break.js.map
}}),
"[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Text),
    "escapeText": (()=>escapeText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
class Text extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextBlot"] {
}
// https://lodash.com/docs#escape
const entityMap = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
};
function escapeText(text) {
    return text.replace(/[&<>"']/g, (s)=>entityMap[s]);
}
;
 //# sourceMappingURL=text.js.map
}}),
"[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
;
;
;
class Inline extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InlineBlot"] {
    static allowedChildren = [
        Inline,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
    ];
    // Lower index means deeper in the DOM tree, since not found (-1) is for embeds
    static order = [
        'cursor',
        'inline',
        // Must be lower
        'link',
        // Chrome wants <a> to be lower
        'underline',
        'strike',
        'italic',
        'bold',
        'script',
        'code' // Must be higher
    ];
    static compare(self, other) {
        const selfIndex = Inline.order.indexOf(self);
        const otherIndex = Inline.order.indexOf(other);
        if (selfIndex >= 0 || otherIndex >= 0) {
            return selfIndex - otherIndex;
        }
        if (self === other) {
            return 0;
        }
        if (self < other) {
            return -1;
        }
        return 1;
    }
    formatAt(index, length, name, value) {
        if (Inline.compare(this.statics.blotName, name) < 0 && this.scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOT)) {
            const blot = this.isolate(index, length);
            if (value) {
                blot.wrap(name, value);
            }
        } else {
            super.formatAt(index, length, name, value);
        }
    }
    optimize(context) {
        super.optimize(context);
        if (this.parent instanceof Inline && Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0) {
            const parent = this.parent.isolate(this.offset(), this.length());
            // @ts-expect-error TODO: make isolate generic
            this.moveChildren(parent);
            parent.wrap(this);
        }
    }
}
const __TURBOPACK__default__export__ = Inline;
 //# sourceMappingURL=inline.js.map
}}),
"[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BlockEmbed": (()=>BlockEmbed),
    "blockDelta": (()=>blockDelta),
    "bubbleFormats": (()=>bubbleFormats),
    "default": (()=>Block)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
;
;
;
;
;
const NEWLINE_LENGTH = 1;
class Block extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockBlot"] {
    cache = {};
    delta() {
        if (this.cache.delta == null) {
            this.cache.delta = blockDelta(this);
        }
        return this.cache.delta;
    }
    deleteAt(index, length) {
        super.deleteAt(index, length);
        this.cache = {};
    }
    formatAt(index, length, name, value) {
        if (length <= 0) return;
        if (this.scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK)) {
            if (index + length === this.length()) {
                this.format(name, value);
            }
        } else {
            super.formatAt(index, Math.min(length, this.length() - index - 1), name, value);
        }
        this.cache = {};
    }
    insertAt(index, value, def) {
        if (def != null) {
            super.insertAt(index, value, def);
            this.cache = {};
            return;
        }
        if (value.length === 0) return;
        const lines = value.split('\n');
        const text = lines.shift();
        if (text.length > 0) {
            if (index < this.length() - 1 || this.children.tail == null) {
                super.insertAt(Math.min(index, this.length() - 1), text);
            } else {
                this.children.tail.insertAt(this.children.tail.length(), text);
            }
            this.cache = {};
        }
        // TODO: Fix this next time the file is edited.
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        let block = this;
        lines.reduce((lineIndex, line)=>{
            // @ts-expect-error Fix me later
            block = block.split(lineIndex, true);
            block.insertAt(0, line);
            return line.length;
        }, index + text.length);
    }
    insertBefore(blot, ref) {
        const { head } = this.children;
        super.insertBefore(blot, ref);
        if (head instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
            head.remove();
        }
        this.cache = {};
    }
    length() {
        if (this.cache.length == null) {
            this.cache.length = super.length() + NEWLINE_LENGTH;
        }
        return this.cache.length;
    }
    moveChildren(target, ref) {
        super.moveChildren(target, ref);
        this.cache = {};
    }
    optimize(context) {
        super.optimize(context);
        this.cache = {};
    }
    path(index) {
        return super.path(index, true);
    }
    removeChild(child) {
        super.removeChild(child);
        this.cache = {};
    }
    split(index) {
        let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {
            const clone = this.clone();
            if (index === 0) {
                this.parent.insertBefore(clone, this);
                return this;
            }
            this.parent.insertBefore(clone, this.next);
            return clone;
        }
        const next = super.split(index, force);
        this.cache = {};
        return next;
    }
}
Block.blotName = 'block';
Block.tagName = 'P';
Block.defaultChild = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
Block.allowedChildren = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
class BlockEmbed extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] {
    attach() {
        super.attach();
        this.attributes = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributorStore"](this.domNode);
    }
    delta() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(this.value(), {
            ...this.formats(),
            ...this.attributes.values()
        });
    }
    format(name, value) {
        const attribute = this.scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK_ATTRIBUTE);
        if (attribute != null) {
            // @ts-expect-error TODO: Scroll#query() should return Attributor when scope is attribute
            this.attributes.attribute(attribute, value);
        }
    }
    formatAt(index, length, name, value) {
        this.format(name, value);
    }
    insertAt(index, value, def) {
        if (def != null) {
            super.insertAt(index, value, def);
            return;
        }
        const lines = value.split('\n');
        const text = lines.pop();
        const blocks = lines.map((line)=>{
            const block = this.scroll.create(Block.blotName);
            block.insertAt(0, line);
            return block;
        });
        const ref = this.split(index);
        blocks.forEach((block)=>{
            this.parent.insertBefore(block, ref);
        });
        if (text) {
            this.parent.insertBefore(this.scroll.create('text', text), ref);
        }
    }
}
BlockEmbed.scope = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK_BLOT;
// It is important for cursor behavior BlockEmbeds use tags that are block level elements
function blockDelta(blot) {
    let filter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    return blot.descendants(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"]).reduce((delta, leaf)=>{
        if (leaf.length() === 0) {
            return delta;
        }
        return delta.insert(leaf.value(), bubbleFormats(leaf, {}, filter));
    }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]()).insert('\n', bubbleFormats(blot));
}
function bubbleFormats(blot) {
    let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    let filter = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
    if (blot == null) return formats;
    if ('formats' in blot && typeof blot.formats === 'function') {
        formats = {
            ...formats,
            ...blot.formats()
        };
        if (filter) {
            // exclude syntax highlighting from deltas and getFormat()
            delete formats['code-token'];
        }
    }
    if (blot.parent == null || blot.parent.statics.blotName === 'scroll' || blot.parent.statics.scope !== blot.statics.scope) {
        return formats;
    }
    return bubbleFormats(blot.parent, formats, filter);
}
;
 //# sourceMappingURL=block.js.map
}}),
"[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
;
;
class Cursor extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] {
    static blotName = 'cursor';
    static className = 'ql-cursor';
    static tagName = 'span';
    static CONTENTS = '\uFEFF';
    static value() {
        return undefined;
    }
    constructor(scroll, domNode, selection){
        super(scroll, domNode);
        this.selection = selection;
        this.textNode = document.createTextNode(Cursor.CONTENTS);
        this.domNode.appendChild(this.textNode);
        this.savedLength = 0;
    }
    detach() {
        // super.detach() will also clear domNode.__blot
        if (this.parent != null) this.parent.removeChild(this);
    }
    format(name, value) {
        if (this.savedLength !== 0) {
            super.format(name, value);
            return;
        }
        // TODO: Fix this next time the file is edited.
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        let target = this;
        let index = 0;
        while(target != null && target.statics.scope !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK_BLOT){
            index += target.offset(target.parent);
            target = target.parent;
        }
        if (target != null) {
            this.savedLength = Cursor.CONTENTS.length;
            // @ts-expect-error TODO: allow empty context in Parchment
            target.optimize();
            target.formatAt(index, Cursor.CONTENTS.length, name, value);
            this.savedLength = 0;
        }
    }
    index(node, offset) {
        if (node === this.textNode) return 0;
        return super.index(node, offset);
    }
    length() {
        return this.savedLength;
    }
    position() {
        return [
            this.textNode,
            this.textNode.data.length
        ];
    }
    remove() {
        super.remove();
        // @ts-expect-error Fix me later
        this.parent = null;
    }
    restore() {
        if (this.selection.composing || this.parent == null) return null;
        const range = this.selection.getNativeRange();
        // Browser may push down styles/nodes inside the cursor blot.
        // https://dvcs.w3.org/hg/editing/raw-file/tip/editing.html#push-down-values
        while(this.domNode.lastChild != null && this.domNode.lastChild !== this.textNode){
            // @ts-expect-error Fix me later
            this.domNode.parentNode.insertBefore(this.domNode.lastChild, this.domNode);
        }
        const prevTextBlot = this.prev instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] ? this.prev : null;
        const prevTextLength = prevTextBlot ? prevTextBlot.length() : 0;
        const nextTextBlot = this.next instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] ? this.next : null;
        // @ts-expect-error TODO: make TextBlot.text public
        const nextText = nextTextBlot ? nextTextBlot.text : '';
        const { textNode } = this;
        // take text from inside this blot and reset it
        const newText = textNode.data.split(Cursor.CONTENTS).join('');
        textNode.data = Cursor.CONTENTS;
        // proactively merge TextBlots around cursor so that optimization
        // doesn't lose the cursor.  the reason we are here in cursor.restore
        // could be that the user clicked in prevTextBlot or nextTextBlot, or
        // the user typed something.
        let mergedTextBlot;
        if (prevTextBlot) {
            mergedTextBlot = prevTextBlot;
            if (newText || nextTextBlot) {
                prevTextBlot.insertAt(prevTextBlot.length(), newText + nextText);
                if (nextTextBlot) {
                    nextTextBlot.remove();
                }
            }
        } else if (nextTextBlot) {
            mergedTextBlot = nextTextBlot;
            nextTextBlot.insertAt(0, newText);
        } else {
            const newTextNode = document.createTextNode(newText);
            mergedTextBlot = this.scroll.create(newTextNode);
            this.parent.insertBefore(mergedTextBlot, this);
        }
        this.remove();
        if (range) {
            // calculate selection to restore
            const remapOffset = (node, offset)=>{
                if (prevTextBlot && node === prevTextBlot.domNode) {
                    return offset;
                }
                if (node === textNode) {
                    return prevTextLength + offset - 1;
                }
                if (nextTextBlot && node === nextTextBlot.domNode) {
                    return prevTextLength + newText.length + offset;
                }
                return null;
            };
            const start = remapOffset(range.start.node, range.start.offset);
            const end = remapOffset(range.end.node, range.end.offset);
            if (start !== null && end !== null) {
                return {
                    startNode: mergedTextBlot.domNode,
                    startOffset: start,
                    endNode: mergedTextBlot.domNode,
                    endOffset: end
                };
            }
        }
        return null;
    }
    update(mutations, context) {
        if (mutations.some((mutation)=>{
            return mutation.type === 'characterData' && mutation.target === this.textNode;
        })) {
            const range = this.restore();
            if (range) context.range = range;
        }
    }
    // Avoid .ql-cursor being a descendant of `<a/>`.
    // The reason is Safari pushes down `<a/>` on text insertion.
    // That will cause DOM nodes not sync with the model.
    //
    // For example ({I} is the caret), given the markup:
    //    <a><span class="ql-cursor">\uFEFF{I}</span></a>
    // When typing a char "x", `<a/>` will be pushed down inside the `<span>` first:
    //    <span class="ql-cursor"><a>\uFEFF{I}</a></span>
    // And then "x" will be inserted after `<a/>`:
    //    <span class="ql-cursor"><a>\uFEFF</a>d{I}</span>
    optimize(context) {
        // @ts-expect-error Fix me later
        super.optimize(context);
        let { parent } = this;
        while(parent){
            if (parent.domNode.tagName === 'A') {
                this.savedLength = Cursor.CONTENTS.length;
                // @ts-expect-error TODO: make isolate generic
                parent.isolate(this.offset(parent), this.length()).unwrap();
                this.savedLength = 0;
                break;
            }
            parent = parent.parent;
        }
    }
    value() {
        return '';
    }
}
const __TURBOPACK__default__export__ = Cursor;
 //# sourceMappingURL=cursor.js.map
}}),
"[project]/node_modules/quill/core/instances.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = new WeakMap();
 //# sourceMappingURL=instances.js.map
}}),
"[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const levels = [
    'error',
    'warn',
    'log',
    'info'
];
let level = 'warn';
function debug(method) {
    if (level) {
        if (levels.indexOf(method) <= levels.indexOf(level)) {
            for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                args[_key - 1] = arguments[_key];
            }
            console[method](...args); // eslint-disable-line no-console
        }
    }
}
function namespace(ns) {
    return levels.reduce((logger, method)=>{
        logger[method] = debug.bind(console, method, ns);
        return logger;
    }, {});
}
namespace.level = (newLevel)=>{
    level = newLevel;
};
debug.level = namespace.level;
const __TURBOPACK__default__export__ = namespace;
 //# sourceMappingURL=logger.js.map
}}),
"[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$node_modules$2f$eventemitter3$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/node_modules/eventemitter3/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__EventEmitter$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/node_modules/eventemitter3/index.js [app-ssr] (ecmascript) <export default as EventEmitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/instances.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill:events');
const EVENTS = [
    'selectionchange',
    'mousedown',
    'mouseup',
    'click'
];
EVENTS.forEach((eventName)=>{
    document.addEventListener(eventName, function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        Array.from(document.querySelectorAll('.ql-container')).forEach((node)=>{
            const quill = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(node);
            if (quill && quill.emitter) {
                quill.emitter.handleDOM(...args);
            }
        });
    });
});
class Emitter extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__EventEmitter$3e$__["EventEmitter"] {
    static events = {
        EDITOR_CHANGE: 'editor-change',
        SCROLL_BEFORE_UPDATE: 'scroll-before-update',
        SCROLL_BLOT_MOUNT: 'scroll-blot-mount',
        SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',
        SCROLL_OPTIMIZE: 'scroll-optimize',
        SCROLL_UPDATE: 'scroll-update',
        SCROLL_EMBED_UPDATE: 'scroll-embed-update',
        SELECTION_CHANGE: 'selection-change',
        TEXT_CHANGE: 'text-change',
        COMPOSITION_BEFORE_START: 'composition-before-start',
        COMPOSITION_START: 'composition-start',
        COMPOSITION_BEFORE_END: 'composition-before-end',
        COMPOSITION_END: 'composition-end'
    };
    static sources = {
        API: 'api',
        SILENT: 'silent',
        USER: 'user'
    };
    constructor(){
        super();
        this.domListeners = {};
        this.on('error', debug.error);
    }
    emit() {
        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){
            args[_key2] = arguments[_key2];
        }
        debug.log.call(debug, ...args);
        // @ts-expect-error
        return super.emit(...args);
    }
    handleDOM(event) {
        for(var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++){
            args[_key3 - 1] = arguments[_key3];
        }
        (this.domListeners[event.type] || []).forEach((_ref)=>{
            let { node, handler } = _ref;
            if (event.target === node || node.contains(event.target)) {
                handler(event, ...args);
            }
        });
    }
    listenDOM(eventName, node, handler) {
        if (!this.domListeners[eventName]) {
            this.domListeners[eventName] = [];
        }
        this.domListeners[eventName].push({
            node,
            handler
        });
    }
}
const __TURBOPACK__default__export__ = Emitter;
 //# sourceMappingURL=emitter.js.map
}}),
"[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Range": (()=>Range),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/cloneDeep.js [app-ssr] (ecmascript) <export default as cloneDeep>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isEqual.js [app-ssr] (ecmascript) <export default as isEqual>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
;
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill:selection');
class Range {
    constructor(index){
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        this.index = index;
        this.length = length;
    }
}
class Selection {
    constructor(scroll, emitter){
        this.emitter = emitter;
        this.scroll = scroll;
        this.composing = false;
        this.mouseDown = false;
        this.root = this.scroll.domNode;
        // @ts-expect-error
        this.cursor = this.scroll.create('cursor', this);
        // savedRange is last non-null range
        this.savedRange = new Range(0, 0);
        this.lastRange = this.savedRange;
        this.lastNative = null;
        this.handleComposition();
        this.handleDragging();
        this.emitter.listenDOM('selectionchange', document, ()=>{
            if (!this.mouseDown && !this.composing) {
                setTimeout(this.update.bind(this, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER), 1);
            }
        });
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_BEFORE_UPDATE, ()=>{
            if (!this.hasFocus()) return;
            const native = this.getNativeRange();
            if (native == null) return;
            if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle
            this.emitter.once(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_UPDATE, (source, mutations)=>{
                try {
                    if (this.root.contains(native.start.node) && this.root.contains(native.end.node)) {
                        this.setNativeRange(native.start.node, native.start.offset, native.end.node, native.end.offset);
                    }
                    const triggeredByTyping = mutations.some((mutation)=>mutation.type === 'characterData' || mutation.type === 'childList' || mutation.type === 'attributes' && mutation.target === this.root);
                    this.update(triggeredByTyping ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT : source);
                } catch (ignored) {
                // ignore
                }
            });
        });
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_OPTIMIZE, (mutations, context)=>{
            if (context.range) {
                const { startNode, startOffset, endNode, endOffset } = context.range;
                this.setNativeRange(startNode, startOffset, endNode, endOffset);
                this.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT);
            }
        });
        this.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT);
    }
    handleComposition() {
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_BEFORE_START, ()=>{
            this.composing = true;
        });
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_END, ()=>{
            this.composing = false;
            if (this.cursor.parent) {
                const range = this.cursor.restore();
                if (!range) return;
                setTimeout(()=>{
                    this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);
                }, 1);
            }
        });
    }
    handleDragging() {
        this.emitter.listenDOM('mousedown', document.body, ()=>{
            this.mouseDown = true;
        });
        this.emitter.listenDOM('mouseup', document.body, ()=>{
            this.mouseDown = false;
            this.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
        });
    }
    focus() {
        if (this.hasFocus()) return;
        this.root.focus({
            preventScroll: true
        });
        this.setRange(this.savedRange);
    }
    format(format, value) {
        this.scroll.update();
        const nativeRange = this.getNativeRange();
        if (nativeRange == null || !nativeRange.native.collapsed || this.scroll.query(format, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK)) return;
        if (nativeRange.start.node !== this.cursor.textNode) {
            const blot = this.scroll.find(nativeRange.start.node, false);
            if (blot == null) return;
            // TODO Give blot ability to not split
            if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"]) {
                const after = blot.split(nativeRange.start.offset);
                blot.parent.insertBefore(this.cursor, after);
            } else {
                // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature
                blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen
            }
            this.cursor.attach();
        }
        this.cursor.format(format, value);
        this.scroll.optimize();
        this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);
        this.update();
    }
    getBounds(index) {
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        const scrollLength = this.scroll.length();
        index = Math.min(index, scrollLength - 1);
        length = Math.min(index + length, scrollLength - 1) - index;
        let node;
        let [leaf, offset] = this.scroll.leaf(index);
        if (leaf == null) return null;
        if (length > 0 && offset === leaf.length()) {
            const [next] = this.scroll.leaf(index + 1);
            if (next) {
                const [line] = this.scroll.line(index);
                const [nextLine] = this.scroll.line(index + 1);
                if (line === nextLine) {
                    leaf = next;
                    offset = 0;
                }
            }
        }
        [node, offset] = leaf.position(offset, true);
        const range = document.createRange();
        if (length > 0) {
            range.setStart(node, offset);
            [leaf, offset] = this.scroll.leaf(index + length);
            if (leaf == null) return null;
            [node, offset] = leaf.position(offset, true);
            range.setEnd(node, offset);
            return range.getBoundingClientRect();
        }
        let side = 'left';
        let rect;
        if (node instanceof Text) {
            // Return null if the text node is empty because it is
            // not able to get a useful client rect:
            // https://github.com/w3c/csswg-drafts/issues/2514.
            // Empty text nodes are most likely caused by TextBlot#optimize()
            // not getting called when editor content changes.
            if (!node.data.length) {
                return null;
            }
            if (offset < node.data.length) {
                range.setStart(node, offset);
                range.setEnd(node, offset + 1);
            } else {
                range.setStart(node, offset - 1);
                range.setEnd(node, offset);
                side = 'right';
            }
            rect = range.getBoundingClientRect();
        } else {
            if (!(leaf.domNode instanceof Element)) return null;
            rect = leaf.domNode.getBoundingClientRect();
            if (offset > 0) side = 'right';
        }
        return {
            bottom: rect.top + rect.height,
            height: rect.height,
            left: rect[side],
            right: rect[side],
            top: rect.top,
            width: 0
        };
    }
    getNativeRange() {
        const selection = document.getSelection();
        if (selection == null || selection.rangeCount <= 0) return null;
        const nativeRange = selection.getRangeAt(0);
        if (nativeRange == null) return null;
        const range = this.normalizeNative(nativeRange);
        debug.info('getNativeRange', range);
        return range;
    }
    getRange() {
        const root = this.scroll.domNode;
        if ('isConnected' in root && !root.isConnected) {
            // document.getSelection() forces layout on Blink, so we trend to
            // not calling it.
            return [
                null,
                null
            ];
        }
        const normalized = this.getNativeRange();
        if (normalized == null) return [
            null,
            null
        ];
        const range = this.normalizedToRange(normalized);
        return [
            range,
            normalized
        ];
    }
    hasFocus() {
        return document.activeElement === this.root || document.activeElement != null && contains(this.root, document.activeElement);
    }
    normalizedToRange(range) {
        const positions = [
            [
                range.start.node,
                range.start.offset
            ]
        ];
        if (!range.native.collapsed) {
            positions.push([
                range.end.node,
                range.end.offset
            ]);
        }
        const indexes = positions.map((position)=>{
            const [node, offset] = position;
            const blot = this.scroll.find(node, true);
            // @ts-expect-error Fix me later
            const index = blot.offset(this.scroll);
            if (offset === 0) {
                return index;
            }
            if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"]) {
                return index + blot.index(node, offset);
            }
            // @ts-expect-error Fix me later
            return index + blot.length();
        });
        const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);
        const start = Math.min(end, ...indexes);
        return new Range(start, end - start);
    }
    normalizeNative(nativeRange) {
        if (!contains(this.root, nativeRange.startContainer) || !nativeRange.collapsed && !contains(this.root, nativeRange.endContainer)) {
            return null;
        }
        const range = {
            start: {
                node: nativeRange.startContainer,
                offset: nativeRange.startOffset
            },
            end: {
                node: nativeRange.endContainer,
                offset: nativeRange.endOffset
            },
            native: nativeRange
        };
        [
            range.start,
            range.end
        ].forEach((position)=>{
            let { node, offset } = position;
            while(!(node instanceof Text) && node.childNodes.length > 0){
                if (node.childNodes.length > offset) {
                    node = node.childNodes[offset];
                    offset = 0;
                } else if (node.childNodes.length === offset) {
                    // @ts-expect-error Fix me later
                    node = node.lastChild;
                    if (node instanceof Text) {
                        offset = node.data.length;
                    } else if (node.childNodes.length > 0) {
                        // Container case
                        offset = node.childNodes.length;
                    } else {
                        // Embed case
                        offset = node.childNodes.length + 1;
                    }
                } else {
                    break;
                }
            }
            position.node = node;
            position.offset = offset;
        });
        return range;
    }
    rangeToNative(range) {
        const scrollLength = this.scroll.length();
        const getPosition = (index, inclusive)=>{
            index = Math.min(scrollLength - 1, index);
            const [leaf, leafOffset] = this.scroll.leaf(index);
            return leaf ? leaf.position(leafOffset, inclusive) : [
                null,
                -1
            ];
        };
        return [
            ...getPosition(range.index, false),
            ...getPosition(range.index + range.length, true)
        ];
    }
    setNativeRange(startNode, startOffset) {
        let endNode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : startNode;
        let endOffset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : startOffset;
        let force = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
        debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);
        if (startNode != null && (this.root.parentNode == null || startNode.parentNode == null || // @ts-expect-error Fix me later
        endNode.parentNode == null)) {
            return;
        }
        const selection = document.getSelection();
        if (selection == null) return;
        if (startNode != null) {
            if (!this.hasFocus()) this.root.focus({
                preventScroll: true
            });
            const { native } = this.getNativeRange() || {};
            if (native == null || force || startNode !== native.startContainer || startOffset !== native.startOffset || endNode !== native.endContainer || endOffset !== native.endOffset) {
                if (startNode instanceof Element && startNode.tagName === 'BR') {
                    // @ts-expect-error Fix me later
                    startOffset = Array.from(startNode.parentNode.childNodes).indexOf(startNode);
                    startNode = startNode.parentNode;
                }
                if (endNode instanceof Element && endNode.tagName === 'BR') {
                    // @ts-expect-error Fix me later
                    endOffset = Array.from(endNode.parentNode.childNodes).indexOf(endNode);
                    endNode = endNode.parentNode;
                }
                const range = document.createRange();
                // @ts-expect-error Fix me later
                range.setStart(startNode, startOffset);
                // @ts-expect-error Fix me later
                range.setEnd(endNode, endOffset);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        } else {
            selection.removeAllRanges();
            this.root.blur();
        }
    }
    setRange(range) {
        let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
        if (typeof force === 'string') {
            source = force;
            force = false;
        }
        debug.info('setRange', range);
        if (range != null) {
            const args = this.rangeToNative(range);
            this.setNativeRange(...args, force);
        } else {
            this.setNativeRange(null);
        }
        this.update(source);
    }
    update() {
        let source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER;
        const oldRange = this.lastRange;
        const [lastRange, nativeRange] = this.getRange();
        this.lastRange = lastRange;
        this.lastNative = nativeRange;
        if (this.lastRange != null) {
            this.savedRange = this.lastRange;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__["isEqual"])(oldRange, this.lastRange)) {
            if (!this.composing && nativeRange != null && nativeRange.native.collapsed && nativeRange.start.node !== this.cursor.textNode) {
                const range = this.cursor.restore();
                if (range) {
                    this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);
                }
            }
            const args = [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SELECTION_CHANGE,
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(this.lastRange),
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(oldRange),
                source
            ];
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.EDITOR_CHANGE, ...args);
            if (source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT) {
                this.emitter.emit(...args);
            }
        }
    }
}
function contains(parent, descendant) {
    try {
        // Firefox inserts inaccessible nodes around video elements
        descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions
    } catch (e) {
        return false;
    }
    return parent.contains(descendant);
}
const __TURBOPACK__default__export__ = Selection;
 //# sourceMappingURL=selection.js.map
}}),
"[project]/node_modules/quill/core/editor.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/cloneDeep.js [app-ssr] (ecmascript) <export default as cloneDeep>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isEqual.js [app-ssr] (ecmascript) <export default as isEqual>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/merge.js [app-ssr] (ecmascript) <export default as merge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const ASCII = /^[ -~]*$/;
class Editor {
    constructor(scroll){
        this.scroll = scroll;
        this.delta = this.getDelta();
    }
    applyDelta(delta) {
        this.scroll.update();
        let scrollLength = this.scroll.length();
        this.scroll.batchStart();
        const normalizedDelta = normalizeDelta(delta);
        const deleteDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        const normalizedOps = splitOpLines(normalizedDelta.ops.slice());
        normalizedOps.reduce((index, op)=>{
            const length = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Op"].length(op);
            let attributes = op.attributes || {};
            let isImplicitNewlinePrepended = false;
            let isImplicitNewlineAppended = false;
            if (op.insert != null) {
                deleteDelta.retain(length);
                if (typeof op.insert === 'string') {
                    const text = op.insert;
                    isImplicitNewlineAppended = !text.endsWith('\n') && (scrollLength <= index || !!this.scroll.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"], index)[0]);
                    this.scroll.insertAt(index, text);
                    const [line, offset] = this.scroll.line(index);
                    let formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(line));
                    if (line instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                        const [leaf] = line.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], offset);
                        if (leaf) {
                            formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])(formats, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(leaf));
                        }
                    }
                    attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(formats, attributes) || {};
                } else if (typeof op.insert === 'object') {
                    const key = Object.keys(op.insert)[0]; // There should only be one key
                    if (key == null) return index;
                    const isInlineEmbed = this.scroll.query(key, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE) != null;
                    if (isInlineEmbed) {
                        if (scrollLength <= index || !!this.scroll.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"], index)[0]) {
                            isImplicitNewlineAppended = true;
                        }
                    } else if (index > 0) {
                        const [leaf, offset] = this.scroll.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], index - 1);
                        if (leaf instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                            const text = leaf.value();
                            if (text[offset] !== '\n') {
                                isImplicitNewlinePrepended = true;
                            }
                        } else if (leaf instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] && leaf.statics.scope === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE_BLOT) {
                            isImplicitNewlinePrepended = true;
                        }
                    }
                    this.scroll.insertAt(index, key, op.insert[key]);
                    if (isInlineEmbed) {
                        const [leaf] = this.scroll.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], index);
                        if (leaf) {
                            const formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(leaf));
                            attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(formats, attributes) || {};
                        }
                    }
                }
                scrollLength += length;
            } else {
                deleteDelta.push(op);
                if (op.retain !== null && typeof op.retain === 'object') {
                    const key = Object.keys(op.retain)[0];
                    if (key == null) return index;
                    this.scroll.updateEmbedAt(index, key, op.retain[key]);
                }
            }
            Object.keys(attributes).forEach((name)=>{
                this.scroll.formatAt(index, length, name, attributes[name]);
            });
            const prependedLength = isImplicitNewlinePrepended ? 1 : 0;
            const addedLength = isImplicitNewlineAppended ? 1 : 0;
            scrollLength += prependedLength + addedLength;
            deleteDelta.retain(prependedLength);
            deleteDelta.delete(addedLength);
            return index + length + prependedLength + addedLength;
        }, 0);
        deleteDelta.reduce((index, op)=>{
            if (typeof op.delete === 'number') {
                this.scroll.deleteAt(index, op.delete);
                return index;
            }
            return index + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Op"].length(op);
        }, 0);
        this.scroll.batchEnd();
        this.scroll.optimize();
        return this.update(normalizedDelta);
    }
    deleteText(index, length) {
        this.scroll.deleteAt(index, length);
        return this.update(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).delete(length));
    }
    formatLine(index, length) {
        let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        this.scroll.update();
        Object.keys(formats).forEach((format)=>{
            this.scroll.lines(index, Math.max(length, 1)).forEach((line)=>{
                line.format(format, formats[format]);
            });
        });
        this.scroll.optimize();
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).retain(length, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(formats));
        return this.update(delta);
    }
    formatText(index, length) {
        let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        Object.keys(formats).forEach((format)=>{
            this.scroll.formatAt(index, length, format, formats[format]);
        });
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).retain(length, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(formats));
        return this.update(delta);
    }
    getContents(index, length) {
        return this.delta.slice(index, index + length);
    }
    getDelta() {
        return this.scroll.lines().reduce((delta, line)=>{
            return delta.concat(line.delta());
        }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
    }
    getFormat(index) {
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        let lines = [];
        let leaves = [];
        if (length === 0) {
            this.scroll.path(index).forEach((path)=>{
                const [blot] = path;
                if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                    lines.push(blot);
                } else if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"]) {
                    leaves.push(blot);
                }
            });
        } else {
            lines = this.scroll.lines(index, length);
            leaves = this.scroll.descendants(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], index, length);
        }
        const [lineFormats, leafFormats] = [
            lines,
            leaves
        ].map((blots)=>{
            const blot = blots.shift();
            if (blot == null) return {};
            let formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(blot);
            while(Object.keys(formats).length > 0){
                const blot = blots.shift();
                if (blot == null) return formats;
                formats = combineFormats((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(blot), formats);
            }
            return formats;
        });
        return {
            ...lineFormats,
            ...leafFormats
        };
    }
    getHTML(index, length) {
        const [line, lineOffset] = this.scroll.line(index);
        if (line) {
            const lineLength = line.length();
            const isWithinLine = line.length() >= lineOffset + length;
            if (isWithinLine && !(lineOffset === 0 && length === lineLength)) {
                return convertHTML(line, lineOffset, length, true);
            }
            return convertHTML(this.scroll, index, length, true);
        }
        return '';
    }
    getText(index, length) {
        return this.getContents(index, length).filter((op)=>typeof op.insert === 'string').map((op)=>op.insert).join('');
    }
    insertContents(index, contents) {
        const normalizedDelta = normalizeDelta(contents);
        const change = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).concat(normalizedDelta);
        this.scroll.insertContents(index, normalizedDelta);
        return this.update(change);
    }
    insertEmbed(index, embed, value) {
        this.scroll.insertAt(index, embed, value);
        return this.update(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).insert({
            [embed]: value
        }));
    }
    insertText(index, text) {
        let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        text = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        this.scroll.insertAt(index, text);
        Object.keys(formats).forEach((format)=>{
            this.scroll.formatAt(index, text.length, format, formats[format]);
        });
        return this.update(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).insert(text, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(formats)));
    }
    isBlank() {
        if (this.scroll.children.length === 0) return true;
        if (this.scroll.children.length > 1) return false;
        const blot = this.scroll.children.head;
        if (blot?.statics.blotName !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName) return false;
        const block = blot;
        if (block.children.length > 1) return false;
        return block.children.head instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    }
    removeFormat(index, length) {
        const text = this.getText(index, length);
        const [line, offset] = this.scroll.line(index + length);
        let suffixLength = 0;
        let suffix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        if (line != null) {
            suffixLength = line.length() - offset;
            suffix = line.delta().slice(offset, offset + suffixLength - 1).insert('\n');
        }
        const contents = this.getContents(index, length + suffixLength);
        const diff = contents.diff(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(text).concat(suffix));
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).concat(diff);
        return this.applyDelta(delta);
    }
    update(change) {
        let mutations = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
        let selectionInfo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;
        const oldDelta = this.delta;
        if (mutations.length === 1 && mutations[0].type === 'characterData' && // @ts-expect-error Fix me later
        mutations[0].target.data.match(ASCII) && this.scroll.find(mutations[0].target)) {
            // Optimization for character changes
            const textBlot = this.scroll.find(mutations[0].target);
            const formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(textBlot);
            const index = textBlot.offset(this.scroll);
            // @ts-expect-error Fix me later
            const oldValue = mutations[0].oldValue.replace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].CONTENTS, '');
            const oldText = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(oldValue);
            // @ts-expect-error
            const newText = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(textBlot.value());
            const relativeSelectionInfo = selectionInfo && {
                oldRange: shiftRange(selectionInfo.oldRange, -index),
                newRange: shiftRange(selectionInfo.newRange, -index)
            };
            const diffDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).concat(oldText.diff(newText, relativeSelectionInfo));
            change = diffDelta.reduce((delta, op)=>{
                if (op.insert) {
                    return delta.insert(op.insert, formats);
                }
                return delta.push(op);
            }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
            this.delta = oldDelta.compose(change);
        } else {
            this.delta = this.getDelta();
            if (!change || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__["isEqual"])(oldDelta.compose(change), this.delta)) {
                change = oldDelta.diff(this.delta, selectionInfo);
            }
        }
        return change;
    }
}
function convertListHTML(items, lastIndent, types) {
    if (items.length === 0) {
        const [endTag] = getListType(types.pop());
        if (lastIndent <= 0) {
            return `</li></${endTag}>`;
        }
        return `</li></${endTag}>${convertListHTML([], lastIndent - 1, types)}`;
    }
    const [{ child, offset, length, indent, type }, ...rest] = items;
    const [tag, attribute] = getListType(type);
    if (indent > lastIndent) {
        types.push(type);
        if (indent === lastIndent + 1) {
            return `<${tag}><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;
        }
        return `<${tag}><li>${convertListHTML(items, lastIndent + 1, types)}`;
    }
    const previousType = types[types.length - 1];
    if (indent === lastIndent && type === previousType) {
        return `</li><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;
    }
    const [endTag] = getListType(types.pop());
    return `</li></${endTag}>${convertListHTML(items, lastIndent - 1, types)}`;
}
function convertHTML(blot, index, length) {
    let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
    if ('html' in blot && typeof blot.html === 'function') {
        return blot.html(index, length);
    }
    if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
        const escapedText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["escapeText"])(blot.value().slice(index, index + length));
        return escapedText.replaceAll(' ', '&nbsp;');
    }
    if (blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParentBlot"]) {
        // TODO fix API
        if (blot.statics.blotName === 'list-container') {
            const items = [];
            blot.children.forEachAt(index, length, (child, offset, childLength)=>{
                const formats = 'formats' in child && typeof child.formats === 'function' ? child.formats() : {};
                items.push({
                    child,
                    offset,
                    length: childLength,
                    indent: formats.indent || 0,
                    type: formats.list
                });
            });
            return convertListHTML(items, -1, []);
        }
        const parts = [];
        blot.children.forEachAt(index, length, (child, offset, childLength)=>{
            parts.push(convertHTML(child, offset, childLength));
        });
        if (isRoot || blot.statics.blotName === 'list') {
            return parts.join('');
        }
        const { outerHTML, innerHTML } = blot.domNode;
        const [start, end] = outerHTML.split(`>${innerHTML}<`);
        // TODO cleanup
        if (start === '<table') {
            return `<table style="border: 1px solid #000;">${parts.join('')}<${end}`;
        }
        return `${start}>${parts.join('')}<${end}`;
    }
    return blot.domNode instanceof Element ? blot.domNode.outerHTML : '';
}
function combineFormats(formats, combined) {
    return Object.keys(combined).reduce((merged, name)=>{
        if (formats[name] == null) return merged;
        const combinedValue = combined[name];
        if (combinedValue === formats[name]) {
            merged[name] = combinedValue;
        } else if (Array.isArray(combinedValue)) {
            if (combinedValue.indexOf(formats[name]) < 0) {
                merged[name] = combinedValue.concat([
                    formats[name]
                ]);
            } else {
                // If style already exists, don't add to an array, but don't lose other styles
                merged[name] = combinedValue;
            }
        } else {
            merged[name] = [
                combinedValue,
                formats[name]
            ];
        }
        return merged;
    }, {});
}
function getListType(type) {
    const tag = type === 'ordered' ? 'ol' : 'ul';
    switch(type){
        case 'checked':
            return [
                tag,
                ' data-list="checked"'
            ];
        case 'unchecked':
            return [
                tag,
                ' data-list="unchecked"'
            ];
        default:
            return [
                tag,
                ''
            ];
    }
}
function normalizeDelta(delta) {
    return delta.reduce((normalizedDelta, op)=>{
        if (typeof op.insert === 'string') {
            const text = op.insert.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
            return normalizedDelta.insert(text, op.attributes);
        }
        return normalizedDelta.push(op);
    }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
}
function shiftRange(_ref, amount) {
    let { index, length } = _ref;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](index + amount, length);
}
function splitOpLines(ops) {
    const split = [];
    ops.forEach((op)=>{
        if (typeof op.insert === 'string') {
            const lines = op.insert.split('\n');
            lines.forEach((line, index)=>{
                if (index) split.push({
                    insert: '\n',
                    attributes: op.attributes
                });
                if (line) split.push({
                    insert: line,
                    attributes: op.attributes
                });
            });
        } else {
            split.push(op);
        }
    });
    return split;
}
const __TURBOPACK__default__export__ = Editor;
 //# sourceMappingURL=editor.js.map
}}),
"[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
class Module {
    static DEFAULTS = {};
    constructor(quill){
        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        this.quill = quill;
        this.options = options;
    }
}
const __TURBOPACK__default__export__ = Module;
 //# sourceMappingURL=module.js.map
}}),
"[project]/node_modules/quill/blots/embed.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
;
;
const GUARD_TEXT = '\uFEFF';
class Embed extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] {
    constructor(scroll, node){
        super(scroll, node);
        this.contentNode = document.createElement('span');
        this.contentNode.setAttribute('contenteditable', 'false');
        Array.from(this.domNode.childNodes).forEach((childNode)=>{
            this.contentNode.appendChild(childNode);
        });
        this.leftGuard = document.createTextNode(GUARD_TEXT);
        this.rightGuard = document.createTextNode(GUARD_TEXT);
        this.domNode.appendChild(this.leftGuard);
        this.domNode.appendChild(this.contentNode);
        this.domNode.appendChild(this.rightGuard);
    }
    index(node, offset) {
        if (node === this.leftGuard) return 0;
        if (node === this.rightGuard) return 1;
        return super.index(node, offset);
    }
    restore(node) {
        let range = null;
        let textNode;
        const text = node.data.split(GUARD_TEXT).join('');
        if (node === this.leftGuard) {
            if (this.prev instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                const prevLength = this.prev.length();
                this.prev.insertAt(prevLength, text);
                range = {
                    startNode: this.prev.domNode,
                    startOffset: prevLength + text.length
                };
            } else {
                textNode = document.createTextNode(text);
                this.parent.insertBefore(this.scroll.create(textNode), this);
                range = {
                    startNode: textNode,
                    startOffset: text.length
                };
            }
        } else if (node === this.rightGuard) {
            if (this.next instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
                this.next.insertAt(0, text);
                range = {
                    startNode: this.next.domNode,
                    startOffset: text.length
                };
            } else {
                textNode = document.createTextNode(text);
                this.parent.insertBefore(this.scroll.create(textNode), this.next);
                range = {
                    startNode: textNode,
                    startOffset: text.length
                };
            }
        }
        node.data = GUARD_TEXT;
        return range;
    }
    update(mutations, context) {
        mutations.forEach((mutation)=>{
            if (mutation.type === 'characterData' && (mutation.target === this.leftGuard || mutation.target === this.rightGuard)) {
                const range = this.restore(mutation.target);
                if (range) context.range = range;
            }
        });
    }
}
const __TURBOPACK__default__export__ = Embed;
 //# sourceMappingURL=embed.js.map
}}),
"[project]/node_modules/quill/core/composition.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/embed.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
;
;
class Composition {
    isComposing = false;
    constructor(scroll, emitter){
        this.scroll = scroll;
        this.emitter = emitter;
        this.setupListeners();
    }
    setupListeners() {
        this.scroll.domNode.addEventListener('compositionstart', (event)=>{
            if (!this.isComposing) {
                this.handleCompositionStart(event);
            }
        });
        this.scroll.domNode.addEventListener('compositionend', (event)=>{
            if (this.isComposing) {
                // Webkit makes DOM changes after compositionend, so we use microtask to
                // ensure the order.
                // https://bugs.webkit.org/show_bug.cgi?id=31902
                queueMicrotask(()=>{
                    this.handleCompositionEnd(event);
                });
            }
        });
    }
    handleCompositionStart(event) {
        const blot = event.target instanceof Node ? this.scroll.find(event.target, true) : null;
        if (blot && !(blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])) {
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_BEFORE_START, event);
            this.scroll.batchStart();
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_START, event);
            this.isComposing = true;
        }
    }
    handleCompositionEnd(event) {
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_BEFORE_END, event);
        this.scroll.batchEnd();
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.COMPOSITION_END, event);
        this.isComposing = false;
    }
}
const __TURBOPACK__default__export__ = Composition;
 //# sourceMappingURL=composition.js.map
}}),
"[project]/node_modules/quill/core/theme.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
class Theme {
    static DEFAULTS = {
        modules: {}
    };
    static themes = {
        default: Theme
    };
    modules = {};
    constructor(quill, options){
        this.quill = quill;
        this.options = options;
    }
    init() {
        Object.keys(this.options.modules).forEach((name)=>{
            if (this.modules[name] == null) {
                this.addModule(name);
            }
        });
    }
    addModule(name) {
        // @ts-expect-error
        const ModuleClass = this.quill.constructor.import(`modules/${name}`);
        this.modules[name] = new ModuleClass(this.quill, this.options.modules[name] || {});
        return this.modules[name];
    }
}
const __TURBOPACK__default__export__ = Theme;
 //# sourceMappingURL=theme.js.map
}}),
"[project]/node_modules/quill/core/utils/scrollRectIntoView.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const getParentElement = (element)=>element.parentElement || element.getRootNode().host || null;
const getElementRect = (element)=>{
    const rect = element.getBoundingClientRect();
    const scaleX = 'offsetWidth' in element && Math.abs(rect.width) / element.offsetWidth || 1;
    const scaleY = 'offsetHeight' in element && Math.abs(rect.height) / element.offsetHeight || 1;
    return {
        top: rect.top,
        right: rect.left + element.clientWidth * scaleX,
        bottom: rect.top + element.clientHeight * scaleY,
        left: rect.left
    };
};
const paddingValueToInt = (value)=>{
    const number = parseInt(value, 10);
    return Number.isNaN(number) ? 0 : number;
};
// Follow the steps described in https://www.w3.org/TR/cssom-view-1/#element-scrolling-members,
// assuming that the scroll option is set to 'nearest'.
const getScrollDistance = (targetStart, targetEnd, scrollStart, scrollEnd, scrollPaddingStart, scrollPaddingEnd)=>{
    if (targetStart < scrollStart && targetEnd > scrollEnd) {
        return 0;
    }
    if (targetStart < scrollStart) {
        return -(scrollStart - targetStart + scrollPaddingStart);
    }
    if (targetEnd > scrollEnd) {
        return targetEnd - targetStart > scrollEnd - scrollStart ? targetStart + scrollPaddingStart - scrollStart : targetEnd - scrollEnd + scrollPaddingEnd;
    }
    return 0;
};
const scrollRectIntoView = (root, targetRect)=>{
    const document = root.ownerDocument;
    let rect = targetRect;
    let current = root;
    while(current){
        const isDocumentBody = current === document.body;
        const bounding = isDocumentBody ? {
            top: 0,
            right: window.visualViewport?.width ?? document.documentElement.clientWidth,
            bottom: window.visualViewport?.height ?? document.documentElement.clientHeight,
            left: 0
        } : getElementRect(current);
        const style = getComputedStyle(current);
        const scrollDistanceX = getScrollDistance(rect.left, rect.right, bounding.left, bounding.right, paddingValueToInt(style.scrollPaddingLeft), paddingValueToInt(style.scrollPaddingRight));
        const scrollDistanceY = getScrollDistance(rect.top, rect.bottom, bounding.top, bounding.bottom, paddingValueToInt(style.scrollPaddingTop), paddingValueToInt(style.scrollPaddingBottom));
        if (scrollDistanceX || scrollDistanceY) {
            if (isDocumentBody) {
                document.defaultView?.scrollBy(scrollDistanceX, scrollDistanceY);
            } else {
                const { scrollLeft, scrollTop } = current;
                if (scrollDistanceY) {
                    current.scrollTop += scrollDistanceY;
                }
                if (scrollDistanceX) {
                    current.scrollLeft += scrollDistanceX;
                }
                const scrolledLeft = current.scrollLeft - scrollLeft;
                const scrolledTop = current.scrollTop - scrollTop;
                rect = {
                    left: rect.left - scrolledLeft,
                    top: rect.top - scrolledTop,
                    right: rect.right - scrolledLeft,
                    bottom: rect.bottom - scrolledTop
                };
            }
        }
        current = isDocumentBody || style.position === 'fixed' ? null : getParentElement(current);
    }
};
const __TURBOPACK__default__export__ = scrollRectIntoView;
 //# sourceMappingURL=scrollRectIntoView.js.map
}}),
"[project]/node_modules/quill/core/utils/createRegistryWithFormats.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
const MAX_REGISTER_ITERATIONS = 100;
const CORE_FORMATS = [
    'block',
    'break',
    'cursor',
    'inline',
    'scroll',
    'text'
];
const createRegistryWithFormats = (formats, sourceRegistry, debug)=>{
    const registry = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Registry"]();
    CORE_FORMATS.forEach((name)=>{
        const coreBlot = sourceRegistry.query(name);
        if (coreBlot) registry.register(coreBlot);
    });
    formats.forEach((name)=>{
        let format = sourceRegistry.query(name);
        if (!format) {
            debug.error(`Cannot register "${name}" specified in "formats" config. Are you sure it was registered?`);
        }
        let iterations = 0;
        while(format){
            registry.register(format);
            format = 'blotName' in format ? format.requiredContainer ?? null : null;
            iterations += 1;
            if (iterations > MAX_REGISTER_ITERATIONS) {
                debug.error(`Cycle detected in registering blot requiredContainer: "${name}"`);
                break;
            }
        }
    });
    return registry;
};
const __TURBOPACK__default__export__ = createRegistryWithFormats;
 //# sourceMappingURL=createRegistryWithFormats.js.map
}}),
"[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Quill),
    "expandConfig": (()=>expandConfig),
    "globalRegistry": (()=>globalRegistry),
    "overload": (()=>overload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/merge.js [app-ssr] (ecmascript) <export default as merge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$editor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/editor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/instances.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$composition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/composition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/theme.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$scrollRectIntoView$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/utils/scrollRectIntoView.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$createRegistryWithFormats$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/utils/createRegistryWithFormats.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill');
const globalRegistry = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Registry();
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.ParentBlot.uiClass = 'ql-ui';
/**
 * Options for initializing a Quill instance
 */ /**
 * Similar to QuillOptions, but with all properties expanded to their default values,
 * and all selectors resolved to HTMLElements.
 */ class Quill {
    static DEFAULTS = {
        bounds: null,
        modules: {
            clipboard: true,
            keyboard: true,
            history: true,
            uploader: true
        },
        placeholder: '',
        readOnly: false,
        registry: globalRegistry,
        theme: 'default'
    };
    static events = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events;
    static sources = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources;
    static version = typeof "2.0.3" === 'undefined' ? 'dev' : "2.0.3";
    static imports = {
        delta: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        parchment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__,
        'core/module': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        'core/theme': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
    };
    static debug(limit) {
        if (limit === true) {
            limit = 'log';
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].level(limit);
    }
    static find(node) {
        let bubble = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(node) || globalRegistry.find(node, bubble);
    }
    static import(name) {
        if (this.imports[name] == null) {
            debug.error(`Cannot import ${name}. Are you sure it was registered?`);
        }
        return this.imports[name];
    }
    static register() {
        if (typeof (arguments.length <= 0 ? undefined : arguments[0]) !== 'string') {
            const target = arguments.length <= 0 ? undefined : arguments[0];
            const overwrite = !!(arguments.length <= 1 ? undefined : arguments[1]);
            const name = 'attrName' in target ? target.attrName : target.blotName;
            if (typeof name === 'string') {
                // Shortcut for formats:
                // register(Blot | Attributor, overwrite)
                this.register(`formats/${name}`, target, overwrite);
            } else {
                Object.keys(target).forEach((key)=>{
                    this.register(key, target[key], overwrite);
                });
            }
        } else {
            const path = arguments.length <= 0 ? undefined : arguments[0];
            const target = arguments.length <= 1 ? undefined : arguments[1];
            const overwrite = !!(arguments.length <= 2 ? undefined : arguments[2]);
            if (this.imports[path] != null && !overwrite) {
                debug.warn(`Overwriting ${path} with`, target);
            }
            this.imports[path] = target;
            if ((path.startsWith('blots/') || path.startsWith('formats/')) && target && typeof target !== 'boolean' && target.blotName !== 'abstract') {
                globalRegistry.register(target);
            }
            if (typeof target.register === 'function') {
                target.register(globalRegistry);
            }
        }
    }
    constructor(container){
        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        this.options = expandConfig(container, options);
        this.container = this.options.container;
        if (this.container == null) {
            debug.error('Invalid Quill container', container);
            return;
        }
        if (this.options.debug) {
            Quill.debug(this.options.debug);
        }
        const html = this.container.innerHTML.trim();
        this.container.classList.add('ql-container');
        this.container.innerHTML = '';
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].set(this.container, this);
        this.root = this.addContainer('ql-editor');
        this.root.classList.add('ql-blank');
        this.emitter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        const scrollBlotName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.ScrollBlot.blotName;
        const ScrollBlot = this.options.registry.query(scrollBlotName);
        if (!ScrollBlot || !('blotName' in ScrollBlot)) {
            throw new Error(`Cannot initialize Quill without "${scrollBlotName}" blot`);
        }
        this.scroll = new ScrollBlot(this.options.registry, this.root, {
            emitter: this.emitter
        });
        this.editor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$editor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.scroll);
        this.selection = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.scroll, this.emitter);
        this.composition = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$composition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](this.scroll, this.emitter);
        this.theme = new this.options.theme(this, this.options); // eslint-disable-line new-cap
        this.keyboard = this.theme.addModule('keyboard');
        this.clipboard = this.theme.addModule('clipboard');
        this.history = this.theme.addModule('history');
        this.uploader = this.theme.addModule('uploader');
        this.theme.addModule('input');
        this.theme.addModule('uiNode');
        this.theme.init();
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.EDITOR_CHANGE, (type)=>{
            if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.TEXT_CHANGE) {
                this.root.classList.toggle('ql-blank', this.editor.isBlank());
            }
        });
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_UPDATE, (source, mutations)=>{
            const oldRange = this.selection.lastRange;
            const [newRange] = this.selection.getRange();
            const selectionInfo = oldRange && newRange ? {
                oldRange,
                newRange
            } : undefined;
            modify.call(this, ()=>this.editor.update(null, mutations, selectionInfo), source);
        });
        this.emitter.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_EMBED_UPDATE, (blot, delta)=>{
            const oldRange = this.selection.lastRange;
            const [newRange] = this.selection.getRange();
            const selectionInfo = oldRange && newRange ? {
                oldRange,
                newRange
            } : undefined;
            modify.call(this, ()=>{
                const change = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(blot.offset(this)).retain({
                    [blot.statics.blotName]: delta
                });
                return this.editor.update(change, [], selectionInfo);
            }, Quill.sources.USER);
        });
        if (html) {
            const contents = this.clipboard.convert({
                html: `${html}<p><br></p>`,
                text: '\n'
            });
            this.setContents(contents);
        }
        this.history.clear();
        if (this.options.placeholder) {
            this.root.setAttribute('data-placeholder', this.options.placeholder);
        }
        if (this.options.readOnly) {
            this.disable();
        }
        this.allowReadOnlyEdits = false;
    }
    addContainer(container) {
        let refNode = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
        if (typeof container === 'string') {
            const className = container;
            container = document.createElement('div');
            container.classList.add(className);
        }
        this.container.insertBefore(container, refNode);
        return container;
    }
    blur() {
        this.selection.setRange(null);
    }
    deleteText(index, length, source) {
        // @ts-expect-error
        [index, length, , source] = overload(index, length, source);
        return modify.call(this, ()=>{
            return this.editor.deleteText(index, length);
        }, source, index, -1 * length);
    }
    disable() {
        this.enable(false);
    }
    editReadOnly(modifier) {
        this.allowReadOnlyEdits = true;
        const value = modifier();
        this.allowReadOnlyEdits = false;
        return value;
    }
    enable() {
        let enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
        this.scroll.enable(enabled);
        this.container.classList.toggle('ql-disabled', !enabled);
    }
    focus() {
        let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        this.selection.focus();
        if (!options.preventScroll) {
            this.scrollSelectionIntoView();
        }
    }
    format(name, value) {
        let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
        return modify.call(this, ()=>{
            const range = this.getSelection(true);
            let change = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
            if (range == null) return change;
            if (this.scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__.Scope.BLOCK)) {
                change = this.editor.formatLine(range.index, range.length, {
                    [name]: value
                });
            } else if (range.length === 0) {
                this.selection.format(name, value);
                return change;
            } else {
                change = this.editor.formatText(range.index, range.length, {
                    [name]: value
                });
            }
            this.setSelection(range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT);
            return change;
        }, source);
    }
    formatLine(index, length, name, value, source) {
        let formats;
        // eslint-disable-next-line prefer-const
        [index, length, formats, source] = overload(index, length, // @ts-expect-error
        name, value, source);
        return modify.call(this, ()=>{
            return this.editor.formatLine(index, length, formats);
        }, source, index, 0);
    }
    formatText(index, length, name, value, source) {
        let formats;
        // eslint-disable-next-line prefer-const
        [index, length, formats, source] = overload(// @ts-expect-error
        index, length, name, value, source);
        return modify.call(this, ()=>{
            return this.editor.formatText(index, length, formats);
        }, source, index, 0);
    }
    getBounds(index) {
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        let bounds = null;
        if (typeof index === 'number') {
            bounds = this.selection.getBounds(index, length);
        } else {
            bounds = this.selection.getBounds(index.index, index.length);
        }
        if (!bounds) return null;
        const containerBounds = this.container.getBoundingClientRect();
        return {
            bottom: bounds.bottom - containerBounds.top,
            height: bounds.height,
            left: bounds.left - containerBounds.left,
            right: bounds.right - containerBounds.left,
            top: bounds.top - containerBounds.top,
            width: bounds.width
        };
    }
    getContents() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.getLength() - index;
        [index, length] = overload(index, length);
        return this.editor.getContents(index, length);
    }
    getFormat() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.getSelection(true);
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        if (typeof index === 'number') {
            return this.editor.getFormat(index, length);
        }
        return this.editor.getFormat(index.index, index.length);
    }
    getIndex(blot) {
        return blot.offset(this.scroll);
    }
    getLength() {
        return this.scroll.length();
    }
    getLeaf(index) {
        return this.scroll.leaf(index);
    }
    getLine(index) {
        return this.scroll.line(index);
    }
    getLines() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MAX_VALUE;
        if (typeof index !== 'number') {
            return this.scroll.lines(index.index, index.length);
        }
        return this.scroll.lines(index, length);
    }
    getModule(name) {
        return this.theme.modules[name];
    }
    getSelection() {
        let focus = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
        if (focus) this.focus();
        this.update(); // Make sure we access getRange with editor in consistent state
        return this.selection.getRange()[0];
    }
    getSemanticHTML() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        let length = arguments.length > 1 ? arguments[1] : undefined;
        if (typeof index === 'number') {
            length = length ?? this.getLength() - index;
        }
        // @ts-expect-error
        [index, length] = overload(index, length);
        return this.editor.getHTML(index, length);
    }
    getText() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        let length = arguments.length > 1 ? arguments[1] : undefined;
        if (typeof index === 'number') {
            length = length ?? this.getLength() - index;
        }
        // @ts-expect-error
        [index, length] = overload(index, length);
        return this.editor.getText(index, length);
    }
    hasFocus() {
        return this.selection.hasFocus();
    }
    insertEmbed(index, embed, value) {
        let source = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : Quill.sources.API;
        return modify.call(this, ()=>{
            return this.editor.insertEmbed(index, embed, value);
        }, source, index);
    }
    insertText(index, text, name, value, source) {
        let formats;
        // eslint-disable-next-line prefer-const
        // @ts-expect-error
        [index, , formats, source] = overload(index, 0, name, value, source);
        return modify.call(this, ()=>{
            return this.editor.insertText(index, text, formats);
        }, source, index, text.length);
    }
    isEnabled() {
        return this.scroll.isEnabled();
    }
    off() {
        return this.emitter.off(...arguments);
    }
    on() {
        return this.emitter.on(...arguments);
    }
    once() {
        return this.emitter.once(...arguments);
    }
    removeFormat(index, length, source) {
        [index, length, , source] = overload(index, length, source);
        return modify.call(this, ()=>{
            return this.editor.removeFormat(index, length);
        }, source, index);
    }
    scrollRectIntoView(rect) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$scrollRectIntoView$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this.root, rect);
    }
    /**
   * @deprecated Use Quill#scrollSelectionIntoView() instead.
   */ scrollIntoView() {
        console.warn('Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead.');
        this.scrollSelectionIntoView();
    }
    /**
   * Scroll the current selection into the visible area.
   * If the selection is already visible, no scrolling will occur.
   */ scrollSelectionIntoView() {
        const range = this.selection.lastRange;
        const bounds = range && this.selection.getBounds(range.index, range.length);
        if (bounds) {
            this.scrollRectIntoView(bounds);
        }
    }
    setContents(delta) {
        let source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
        return modify.call(this, ()=>{
            delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](delta);
            const length = this.getLength();
            // Quill will set empty editor to \n
            const delete1 = this.editor.deleteText(0, length);
            const applied = this.editor.insertContents(0, delta);
            // Remove extra \n from empty editor initialization
            const delete2 = this.editor.deleteText(this.getLength() - 1, 1);
            return delete1.compose(applied).compose(delete2);
        }, source);
    }
    setSelection(index, length, source) {
        if (index == null) {
            // @ts-expect-error https://github.com/microsoft/TypeScript/issues/22609
            this.selection.setRange(null, length || Quill.sources.API);
        } else {
            // @ts-expect-error
            [index, length, , source] = overload(index, length, source);
            this.selection.setRange(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](Math.max(0, index), length), source);
            if (source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT) {
                this.scrollSelectionIntoView();
            }
        }
    }
    setText(text) {
        let source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(text);
        return this.setContents(delta, source);
    }
    update() {
        let source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER;
        const change = this.scroll.update(source); // Will update selection before selection.update() does if text changes
        this.selection.update(source);
        // TODO this is usually undefined
        return change;
    }
    updateContents(delta) {
        let source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
        return modify.call(this, ()=>{
            delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](delta);
            return this.editor.applyDelta(delta);
        }, source, true);
    }
}
function resolveSelector(selector) {
    return typeof selector === 'string' ? document.querySelector(selector) : selector;
}
function expandModuleConfig(config) {
    return Object.entries(config ?? {}).reduce((expanded, _ref)=>{
        let [key, value] = _ref;
        return {
            ...expanded,
            [key]: value === true ? {} : value
        };
    }, {});
}
function omitUndefinedValuesFromOptions(obj) {
    return Object.fromEntries(Object.entries(obj).filter((entry)=>entry[1] !== undefined));
}
function expandConfig(containerOrSelector, options) {
    const container = resolveSelector(containerOrSelector);
    if (!container) {
        throw new Error('Invalid Quill container');
    }
    const shouldUseDefaultTheme = !options.theme || options.theme === Quill.DEFAULTS.theme;
    const theme = shouldUseDefaultTheme ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] : Quill.import(`themes/${options.theme}`);
    if (!theme) {
        throw new Error(`Invalid theme ${options.theme}. Did you register it?`);
    }
    const { modules: quillModuleDefaults, ...quillDefaults } = Quill.DEFAULTS;
    const { modules: themeModuleDefaults, ...themeDefaults } = theme.DEFAULTS;
    let userModuleOptions = expandModuleConfig(options.modules);
    // Special case toolbar shorthand
    if (userModuleOptions != null && userModuleOptions.toolbar && userModuleOptions.toolbar.constructor !== Object) {
        userModuleOptions = {
            ...userModuleOptions,
            toolbar: {
                container: userModuleOptions.toolbar
            }
        };
    }
    const modules = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, expandModuleConfig(quillModuleDefaults), expandModuleConfig(themeModuleDefaults), userModuleOptions);
    const config = {
        ...quillDefaults,
        ...omitUndefinedValuesFromOptions(themeDefaults),
        ...omitUndefinedValuesFromOptions(options)
    };
    let registry = options.registry;
    if (registry) {
        if (options.formats) {
            debug.warn('Ignoring "formats" option because "registry" is specified');
        }
    } else {
        registry = options.formats ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$createRegistryWithFormats$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(options.formats, config.registry, debug) : config.registry;
    }
    return {
        ...config,
        registry,
        container,
        theme,
        modules: Object.entries(modules).reduce((modulesWithDefaults, _ref2)=>{
            let [name, value] = _ref2;
            if (!value) return modulesWithDefaults;
            const moduleClass = Quill.import(`modules/${name}`);
            if (moduleClass == null) {
                debug.error(`Cannot load ${name} module. Are you sure you registered it?`);
                return modulesWithDefaults;
            }
            return {
                ...modulesWithDefaults,
                // @ts-expect-error
                [name]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, moduleClass.DEFAULTS || {}, value)
            };
        }, {}),
        bounds: resolveSelector(config.bounds)
    };
}
// Handle selection preservation and TEXT_CHANGE emission
// common to modification APIs
function modify(modifier, source, index, shift) {
    if (!this.isEnabled() && source === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER && !this.allowReadOnlyEdits) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
    }
    let range = index == null ? null : this.getSelection();
    const oldDelta = this.editor.delta;
    const change = modifier();
    if (range != null) {
        if (index === true) {
            index = range.index; // eslint-disable-line prefer-destructuring
        }
        if (shift == null) {
            range = shiftRange(range, change, source);
        } else if (shift !== 0) {
            // @ts-expect-error index should always be number
            range = shiftRange(range, index, shift, source);
        }
        this.setSelection(range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT);
    }
    if (change.length() > 0) {
        const args = [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.TEXT_CHANGE,
            change,
            oldDelta,
            source
        ];
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.EDITOR_CHANGE, ...args);
        if (source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT) {
            this.emitter.emit(...args);
        }
    }
    return change;
}
function overload(index, length, name, value, source) {
    let formats = {};
    // @ts-expect-error
    if (typeof index.index === 'number' && typeof index.length === 'number') {
        // Allow for throwaway end (used by insertText/insertEmbed)
        if (typeof length !== 'number') {
            // @ts-expect-error
            source = value;
            value = name;
            name = length;
            // @ts-expect-error
            length = index.length; // eslint-disable-line prefer-destructuring
            // @ts-expect-error
            index = index.index; // eslint-disable-line prefer-destructuring
        } else {
            // @ts-expect-error
            length = index.length; // eslint-disable-line prefer-destructuring
            // @ts-expect-error
            index = index.index; // eslint-disable-line prefer-destructuring
        }
    } else if (typeof length !== 'number') {
        // @ts-expect-error
        source = value;
        value = name;
        name = length;
        length = 0;
    }
    // Handle format being object, two format name/value strings or excluded
    if (typeof name === 'object') {
        // @ts-expect-error Fix me later
        formats = name;
        // @ts-expect-error
        source = value;
    } else if (typeof name === 'string') {
        if (value != null) {
            formats[name] = value;
        } else {
            // @ts-expect-error
            source = name;
        }
    }
    // Handle optional source
    source = source || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.API;
    // @ts-expect-error
    return [
        index,
        length,
        formats,
        source
    ];
}
function shiftRange(range, index, lengthOrSource, source) {
    const length = typeof lengthOrSource === 'number' ? lengthOrSource : 0;
    if (range == null) return null;
    let start;
    let end;
    // @ts-expect-error -- TODO: add a better type guard around `index`
    if (index && typeof index.transformPosition === 'function') {
        [start, end] = [
            range.index,
            range.index + range.length
        ].map((pos)=>// @ts-expect-error -- TODO: add a better type guard around `index`
            index.transformPosition(pos, source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER));
    } else {
        [start, end] = [
            range.index,
            range.index + range.length
        ].map((pos)=>{
            // @ts-expect-error -- TODO: add a better type guard around `index`
            if (pos < index || pos === index && source === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER) return pos;
            if (length >= 0) {
                return pos + length;
            }
            // @ts-expect-error -- TODO: add a better type guard around `index`
            return Math.max(index, pos + length);
        });
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](start, end - start);
}
;
;
 //# sourceMappingURL=quill.js.map
}}),
"[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$editor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/editor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$instances$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/instances.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$composition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/composition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/theme.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$scrollRectIntoView$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/utils/scrollRectIntoView.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$utils$2f$createRegistryWithFormats$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/utils/createRegistryWithFormats.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
class Container extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContainerBlot"] {
}
const __TURBOPACK__default__export__ = Container;
 //# sourceMappingURL=container.js.map
}}),
"[project]/node_modules/quill/blots/scroll.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
function isLine(blot) {
    return blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] || blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"];
}
function isUpdatable(blot) {
    return typeof blot.updateContent === 'function';
}
class Scroll extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ScrollBlot"] {
    static blotName = 'scroll';
    static className = 'ql-editor';
    static tagName = 'DIV';
    static defaultChild = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    static allowedChildren = [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
    ];
    constructor(registry, domNode, _ref){
        let { emitter } = _ref;
        super(registry, domNode);
        this.emitter = emitter;
        this.batch = false;
        this.optimize();
        this.enable();
        this.domNode.addEventListener('dragstart', (e)=>this.handleDragStart(e));
    }
    batchStart() {
        if (!Array.isArray(this.batch)) {
            this.batch = [];
        }
    }
    batchEnd() {
        if (!this.batch) return;
        const mutations = this.batch;
        this.batch = false;
        this.update(mutations);
    }
    emitMount(blot) {
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_BLOT_MOUNT, blot);
    }
    emitUnmount(blot) {
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_BLOT_UNMOUNT, blot);
    }
    emitEmbedUpdate(blot, change) {
        this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_EMBED_UPDATE, blot, change);
    }
    deleteAt(index, length) {
        const [first, offset] = this.line(index);
        const [last] = this.line(index + length);
        super.deleteAt(index, length);
        if (last != null && first !== last && offset > 0) {
            if (first instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"] || last instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"]) {
                this.optimize();
                return;
            }
            const ref = last.children.head instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] ? null : last.children.head;
            // @ts-expect-error
            first.moveChildren(last, ref);
            // @ts-expect-error
            first.remove();
        }
        this.optimize();
    }
    enable() {
        let enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
        this.domNode.setAttribute('contenteditable', enabled ? 'true' : 'false');
    }
    formatAt(index, length, format, value) {
        super.formatAt(index, length, format, value);
        this.optimize();
    }
    insertAt(index, value, def) {
        if (index >= this.length()) {
            if (def == null || this.scroll.query(value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK) == null) {
                const blot = this.scroll.create(this.statics.defaultChild.blotName);
                this.appendChild(blot);
                if (def == null && value.endsWith('\n')) {
                    blot.insertAt(0, value.slice(0, -1), def);
                } else {
                    blot.insertAt(0, value, def);
                }
            } else {
                const embed = this.scroll.create(value, def);
                this.appendChild(embed);
            }
        } else {
            super.insertAt(index, value, def);
        }
        this.optimize();
    }
    insertBefore(blot, ref) {
        if (blot.statics.scope === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE_BLOT) {
            const wrapper = this.scroll.create(this.statics.defaultChild.blotName);
            wrapper.appendChild(blot);
            super.insertBefore(wrapper, ref);
        } else {
            super.insertBefore(blot, ref);
        }
    }
    insertContents(index, delta) {
        const renderBlocks = this.deltaToRenderBlocks(delta.concat(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert('\n')));
        const last = renderBlocks.pop();
        if (last == null) return;
        this.batchStart();
        const first = renderBlocks.shift();
        if (first) {
            const shouldInsertNewlineChar = first.type === 'block' && (first.delta.length() === 0 || !this.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"], index)[0] && index < this.length());
            const delta = first.type === 'block' ? first.delta : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert({
                [first.key]: first.value
            });
            insertInlineContents(this, index, delta);
            const newlineCharLength = first.type === 'block' ? 1 : 0;
            const lineEndIndex = index + delta.length() + newlineCharLength;
            if (shouldInsertNewlineChar) {
                this.insertAt(lineEndIndex - 1, '\n');
            }
            const formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(this.line(index)[0]);
            const attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(formats, first.attributes) || {};
            Object.keys(attributes).forEach((name)=>{
                this.formatAt(lineEndIndex - 1, 1, name, attributes[name]);
            });
            index = lineEndIndex;
        }
        let [refBlot, refBlotOffset] = this.children.find(index);
        if (renderBlocks.length) {
            if (refBlot) {
                refBlot = refBlot.split(refBlotOffset);
                refBlotOffset = 0;
            }
            renderBlocks.forEach((renderBlock)=>{
                if (renderBlock.type === 'block') {
                    const block = this.createBlock(renderBlock.attributes, refBlot || undefined);
                    insertInlineContents(block, 0, renderBlock.delta);
                } else {
                    const blockEmbed = this.create(renderBlock.key, renderBlock.value);
                    this.insertBefore(blockEmbed, refBlot || undefined);
                    Object.keys(renderBlock.attributes).forEach((name)=>{
                        blockEmbed.format(name, renderBlock.attributes[name]);
                    });
                }
            });
        }
        if (last.type === 'block' && last.delta.length()) {
            const offset = refBlot ? refBlot.offset(refBlot.scroll) + refBlotOffset : this.length();
            insertInlineContents(this, offset, last.delta);
        }
        this.batchEnd();
        this.optimize();
    }
    isEnabled() {
        return this.domNode.getAttribute('contenteditable') === 'true';
    }
    leaf(index) {
        const last = this.path(index).pop();
        if (!last) {
            return [
                null,
                -1
            ];
        }
        const [blot, offset] = last;
        return blot instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"] ? [
            blot,
            offset
        ] : [
            null,
            -1
        ];
    }
    line(index) {
        if (index === this.length()) {
            return this.line(index - 1);
        }
        // @ts-expect-error TODO: make descendant() generic
        return this.descendant(isLine, index);
    }
    lines() {
        let index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
        let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Number.MAX_VALUE;
        const getLines = (blot, blotIndex, blotLength)=>{
            let lines = [];
            let lengthLeft = blotLength;
            blot.children.forEachAt(blotIndex, blotLength, (child, childIndex, childLength)=>{
                if (isLine(child)) {
                    lines.push(child);
                } else if (child instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContainerBlot"]) {
                    lines = lines.concat(getLines(child, childIndex, lengthLeft));
                }
                lengthLeft -= childLength;
            });
            return lines;
        };
        return getLines(this, index, length);
    }
    optimize() {
        let mutations = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
        let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        if (this.batch) return;
        super.optimize(mutations, context);
        if (mutations.length > 0) {
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_OPTIMIZE, mutations, context);
        }
    }
    path(index) {
        return super.path(index).slice(1); // Exclude self
    }
    remove() {
    // Never remove self
    }
    update(mutations) {
        if (this.batch) {
            if (Array.isArray(mutations)) {
                this.batch = this.batch.concat(mutations);
            }
            return;
        }
        let source = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER;
        if (typeof mutations === 'string') {
            source = mutations;
        }
        if (!Array.isArray(mutations)) {
            mutations = this.observer.takeRecords();
        }
        mutations = mutations.filter((_ref2)=>{
            let { target } = _ref2;
            const blot = this.find(target, true);
            return blot && !isUpdatable(blot);
        });
        if (mutations.length > 0) {
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_BEFORE_UPDATE, source, mutations);
        }
        super.update(mutations.concat([])); // pass copy
        if (mutations.length > 0) {
            this.emitter.emit(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_UPDATE, source, mutations);
        }
    }
    updateEmbedAt(index, key, change) {
        // Currently it only supports top-level embeds (BlockEmbed).
        // We can update `ParentBlot` in parchment to support inline embeds.
        const [blot] = this.descendant((b)=>b instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"], index);
        if (blot && blot.statics.blotName === key && isUpdatable(blot)) {
            blot.updateContent(change);
        }
    }
    handleDragStart(event) {
        event.preventDefault();
    }
    deltaToRenderBlocks(delta) {
        const renderBlocks = [];
        let currentBlockDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
        delta.forEach((op)=>{
            const insert = op?.insert;
            if (!insert) return;
            if (typeof insert === 'string') {
                const splitted = insert.split('\n');
                splitted.slice(0, -1).forEach((text)=>{
                    currentBlockDelta.insert(text, op.attributes);
                    renderBlocks.push({
                        type: 'block',
                        delta: currentBlockDelta,
                        attributes: op.attributes ?? {}
                    });
                    currentBlockDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                });
                const last = splitted[splitted.length - 1];
                if (last) {
                    currentBlockDelta.insert(last, op.attributes);
                }
            } else {
                const key = Object.keys(insert)[0];
                if (!key) return;
                if (this.query(key, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE)) {
                    currentBlockDelta.push(op);
                } else {
                    if (currentBlockDelta.length()) {
                        renderBlocks.push({
                            type: 'block',
                            delta: currentBlockDelta,
                            attributes: {}
                        });
                    }
                    currentBlockDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
                    renderBlocks.push({
                        type: 'blockEmbed',
                        key,
                        value: insert[key],
                        attributes: op.attributes ?? {}
                    });
                }
            }
        });
        if (currentBlockDelta.length()) {
            renderBlocks.push({
                type: 'block',
                delta: currentBlockDelta,
                attributes: {}
            });
        }
        return renderBlocks;
    }
    createBlock(attributes, refBlot) {
        let blotName;
        const formats = {};
        Object.entries(attributes).forEach((_ref3)=>{
            let [key, value] = _ref3;
            const isBlockBlot = this.query(key, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOT) != null;
            if (isBlockBlot) {
                blotName = key;
            } else {
                formats[key] = value;
            }
        });
        const block = this.create(blotName || this.statics.defaultChild.blotName, blotName ? attributes[blotName] : undefined);
        this.insertBefore(block, refBlot || undefined);
        const length = block.length();
        Object.entries(formats).forEach((_ref4)=>{
            let [key, value] = _ref4;
            block.formatAt(0, length, key, value);
        });
        return block;
    }
}
function insertInlineContents(parent, index, inlineContents) {
    inlineContents.reduce((index, op)=>{
        const length = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Op"].length(op);
        let attributes = op.attributes || {};
        if (op.insert != null) {
            if (typeof op.insert === 'string') {
                const text = op.insert;
                parent.insertAt(index, text);
                const [leaf] = parent.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], index);
                const formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(leaf);
                attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(formats, attributes) || {};
            } else if (typeof op.insert === 'object') {
                const key = Object.keys(op.insert)[0]; // There should only be one key
                if (key == null) return index;
                parent.insertAt(index, key, op.insert[key]);
                const isInlineEmbed = parent.scroll.query(key, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE) != null;
                if (isInlineEmbed) {
                    const [leaf] = parent.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LeafBlot"], index);
                    const formats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["bubbleFormats"])(leaf);
                    attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(formats, attributes) || {};
                }
            }
        }
        Object.keys(attributes).forEach((key)=>{
            parent.formatAt(index, length, key, attributes[key]);
        });
        return index + length;
    }, index);
}
const __TURBOPACK__default__export__ = Scroll;
 //# sourceMappingURL=scroll.js.map
}}),
"[project]/node_modules/quill/formats/align.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AlignAttribute": (()=>AlignAttribute),
    "AlignClass": (()=>AlignClass),
    "AlignStyle": (()=>AlignStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
const config = {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK,
    whitelist: [
        'right',
        'center',
        'justify'
    ]
};
const AlignAttribute = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Attributor"]('align', 'align', config);
const AlignClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('align', 'ql-align', config);
const AlignStyle = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"]('align', 'text-align', config);
;
 //# sourceMappingURL=align.js.map
}}),
"[project]/node_modules/quill/formats/color.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ColorAttributor": (()=>ColorAttributor),
    "ColorClass": (()=>ColorClass),
    "ColorStyle": (()=>ColorStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
class ColorAttributor extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"] {
    value(domNode) {
        let value = super.value(domNode);
        if (!value.startsWith('rgb(')) return value;
        value = value.replace(/^[^\d]+/, '').replace(/[^\d]+$/, '');
        const hex = value.split(',').map((component)=>`00${parseInt(component, 10).toString(16)}`.slice(-2)).join('');
        return `#${hex}`;
    }
}
const ColorClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('color', 'ql-color', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE
});
const ColorStyle = new ColorAttributor('color', 'color', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE
});
;
 //# sourceMappingURL=color.js.map
}}),
"[project]/node_modules/quill/formats/background.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BackgroundClass": (()=>BackgroundClass),
    "BackgroundStyle": (()=>BackgroundStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/color.js [app-ssr] (ecmascript)");
;
;
const BackgroundClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('background', 'ql-bg', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE
});
const BackgroundStyle = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorAttributor"]('background', 'background-color', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE
});
;
 //# sourceMappingURL=background.js.map
}}),
"[project]/node_modules/quill/formats/code.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Code": (()=>Code),
    "CodeBlockContainer": (()=>CodeBlockContainer),
    "default": (()=>CodeBlock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
class CodeBlockContainer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static create(value) {
        const domNode = super.create(value);
        domNode.setAttribute('spellcheck', 'false');
        return domNode;
    }
    code(index, length) {
        return this.children// @ts-expect-error
        .map((child)=>child.length() <= 1 ? '' : child.domNode.innerText).join('\n').slice(index, index + length);
    }
    html(index, length) {
        // `\n`s are needed in order to support empty lines at the beginning and the end.
        // https://html.spec.whatwg.org/multipage/syntax.html#element-restrictions
        return `<pre>\n${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["escapeText"])(this.code(index, length))}\n</pre>`;
    }
}
class CodeBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static TAB = '  ';
    static register() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(CodeBlockContainer);
    }
}
class Code extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
}
Code.blotName = 'code';
Code.tagName = 'CODE';
CodeBlock.blotName = 'code-block';
CodeBlock.className = 'ql-code-block';
CodeBlock.tagName = 'DIV';
CodeBlockContainer.blotName = 'code-block-container';
CodeBlockContainer.className = 'ql-code-block-container';
CodeBlockContainer.tagName = 'DIV';
CodeBlockContainer.allowedChildren = [
    CodeBlock
];
CodeBlock.allowedChildren = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
CodeBlock.requiredContainer = CodeBlockContainer;
;
 //# sourceMappingURL=code.js.map
}}),
"[project]/node_modules/quill/formats/direction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DirectionAttribute": (()=>DirectionAttribute),
    "DirectionClass": (()=>DirectionClass),
    "DirectionStyle": (()=>DirectionStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
const config = {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK,
    whitelist: [
        'rtl'
    ]
};
const DirectionAttribute = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Attributor"]('direction', 'dir', config);
const DirectionClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('direction', 'ql-direction', config);
const DirectionStyle = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"]('direction', 'direction', config);
;
 //# sourceMappingURL=direction.js.map
}}),
"[project]/node_modules/quill/formats/font.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FontClass": (()=>FontClass),
    "FontStyle": (()=>FontStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
const config = {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE,
    whitelist: [
        'serif',
        'monospace'
    ]
};
const FontClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('font', 'ql-font', config);
class FontStyleAttributor extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"] {
    value(node) {
        return super.value(node).replace(/["']/g, '');
    }
}
const FontStyle = new FontStyleAttributor('font', 'font-family', config);
;
 //# sourceMappingURL=font.js.map
}}),
"[project]/node_modules/quill/formats/size.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SizeClass": (()=>SizeClass),
    "SizeStyle": (()=>SizeStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
const SizeClass = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('size', 'ql-size', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE,
    whitelist: [
        'small',
        'large',
        'huge'
    ]
});
const SizeStyle = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"]('size', 'font-size', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE,
    whitelist: [
        '10px',
        '18px',
        '32px'
    ]
});
;
 //# sourceMappingURL=size.js.map
}}),
"[project]/node_modules/quill/modules/keyboard.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SHORTKEY": (()=>SHORTKEY),
    "default": (()=>Keyboard),
    "deleteRange": (()=>deleteRange),
    "normalize": (()=>normalize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/cloneDeep.js [app-ssr] (ecmascript) <export default as cloneDeep>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/isEqual.js [app-ssr] (ecmascript) <export default as isEqual>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill:keyboard');
const SHORTKEY = /Mac/i.test(navigator.platform) ? 'metaKey' : 'ctrlKey';
class Keyboard extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static match(evt, binding) {
        if ([
            'altKey',
            'ctrlKey',
            'metaKey',
            'shiftKey'
        ].some((key)=>{
            return !!binding[key] !== evt[key] && binding[key] !== null;
        })) {
            return false;
        }
        return binding.key === evt.key || binding.key === evt.which;
    }
    constructor(quill, options){
        super(quill, options);
        this.bindings = {};
        // @ts-expect-error Fix me later
        Object.keys(this.options.bindings).forEach((name)=>{
            // @ts-expect-error Fix me later
            if (this.options.bindings[name]) {
                // @ts-expect-error Fix me later
                this.addBinding(this.options.bindings[name]);
            }
        });
        this.addBinding({
            key: 'Enter',
            shiftKey: null
        }, this.handleEnter);
        this.addBinding({
            key: 'Enter',
            metaKey: null,
            ctrlKey: null,
            altKey: null
        }, ()=>{});
        if (/Firefox/i.test(navigator.userAgent)) {
            // Need to handle delete and backspace for Firefox in the general case #1171
            this.addBinding({
                key: 'Backspace'
            }, {
                collapsed: true
            }, this.handleBackspace);
            this.addBinding({
                key: 'Delete'
            }, {
                collapsed: true
            }, this.handleDelete);
        } else {
            this.addBinding({
                key: 'Backspace'
            }, {
                collapsed: true,
                prefix: /^.?$/
            }, this.handleBackspace);
            this.addBinding({
                key: 'Delete'
            }, {
                collapsed: true,
                suffix: /^.?$/
            }, this.handleDelete);
        }
        this.addBinding({
            key: 'Backspace'
        }, {
            collapsed: false
        }, this.handleDeleteRange);
        this.addBinding({
            key: 'Delete'
        }, {
            collapsed: false
        }, this.handleDeleteRange);
        this.addBinding({
            key: 'Backspace',
            altKey: null,
            ctrlKey: null,
            metaKey: null,
            shiftKey: null
        }, {
            collapsed: true,
            offset: 0
        }, this.handleBackspace);
        this.listen();
    }
    addBinding(keyBinding) {
        let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        let handler = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        const binding = normalize(keyBinding);
        if (binding == null) {
            debug.warn('Attempted to add invalid keyboard binding', binding);
            return;
        }
        if (typeof context === 'function') {
            context = {
                handler: context
            };
        }
        if (typeof handler === 'function') {
            handler = {
                handler
            };
        }
        const keys = Array.isArray(binding.key) ? binding.key : [
            binding.key
        ];
        keys.forEach((key)=>{
            const singleBinding = {
                ...binding,
                key,
                ...context,
                ...handler
            };
            this.bindings[singleBinding.key] = this.bindings[singleBinding.key] || [];
            this.bindings[singleBinding.key].push(singleBinding);
        });
    }
    listen() {
        this.quill.root.addEventListener('keydown', (evt)=>{
            if (evt.defaultPrevented || evt.isComposing) return;
            // evt.isComposing is false when pressing Enter/Backspace when composing in Safari
            // https://bugs.webkit.org/show_bug.cgi?id=165004
            const isComposing = evt.keyCode === 229 && (evt.key === 'Enter' || evt.key === 'Backspace');
            if (isComposing) return;
            const bindings = (this.bindings[evt.key] || []).concat(this.bindings[evt.which] || []);
            const matches = bindings.filter((binding)=>Keyboard.match(evt, binding));
            if (matches.length === 0) return;
            // @ts-expect-error
            const blot = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].find(evt.target, true);
            if (blot && blot.scroll !== this.quill.scroll) return;
            const range = this.quill.getSelection();
            if (range == null || !this.quill.hasFocus()) return;
            const [line, offset] = this.quill.getLine(range.index);
            const [leafStart, offsetStart] = this.quill.getLeaf(range.index);
            const [leafEnd, offsetEnd] = range.length === 0 ? [
                leafStart,
                offsetStart
            ] : this.quill.getLeaf(range.index + range.length);
            const prefixText = leafStart instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextBlot"] ? leafStart.value().slice(0, offsetStart) : '';
            const suffixText = leafEnd instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TextBlot"] ? leafEnd.value().slice(offsetEnd) : '';
            const curContext = {
                collapsed: range.length === 0,
                // @ts-expect-error Fix me later
                empty: range.length === 0 && line.length() <= 1,
                format: this.quill.getFormat(range),
                line,
                offset,
                prefix: prefixText,
                suffix: suffixText,
                event: evt
            };
            const prevented = matches.some((binding)=>{
                if (binding.collapsed != null && binding.collapsed !== curContext.collapsed) {
                    return false;
                }
                if (binding.empty != null && binding.empty !== curContext.empty) {
                    return false;
                }
                if (binding.offset != null && binding.offset !== curContext.offset) {
                    return false;
                }
                if (Array.isArray(binding.format)) {
                    // any format is present
                    if (binding.format.every((name)=>curContext.format[name] == null)) {
                        return false;
                    }
                } else if (typeof binding.format === 'object') {
                    // all formats must match
                    if (!Object.keys(binding.format).every((name)=>{
                        // @ts-expect-error Fix me later
                        if (binding.format[name] === true) return curContext.format[name] != null;
                        // @ts-expect-error Fix me later
                        if (binding.format[name] === false) return curContext.format[name] == null;
                        // @ts-expect-error Fix me later
                        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__isEqual$3e$__["isEqual"])(binding.format[name], curContext.format[name]);
                    })) {
                        return false;
                    }
                }
                if (binding.prefix != null && !binding.prefix.test(curContext.prefix)) {
                    return false;
                }
                if (binding.suffix != null && !binding.suffix.test(curContext.suffix)) {
                    return false;
                }
                // @ts-expect-error Fix me later
                return binding.handler.call(this, range, curContext, binding) !== true;
            });
            if (prevented) {
                evt.preventDefault();
            }
        });
    }
    handleBackspace(range, context) {
        // Check for astral symbols
        const length = /[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(context.prefix) ? 2 : 1;
        if (range.index === 0 || this.quill.getLength() <= 1) return;
        let formats = {};
        const [line] = this.quill.getLine(range.index);
        let delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index - length).delete(length);
        if (context.offset === 0) {
            // Always deleting newline here, length always 1
            const [prev] = this.quill.getLine(range.index - 1);
            if (prev) {
                const isPrevLineEmpty = prev.statics.blotName === 'block' && prev.length() <= 1;
                if (!isPrevLineEmpty) {
                    // @ts-expect-error Fix me later
                    const curFormats = line.formats();
                    const prevFormats = this.quill.getFormat(range.index - 1, 1);
                    formats = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(curFormats, prevFormats) || {};
                    if (Object.keys(formats).length > 0) {
                        // line.length() - 1 targets \n in line, another -1 for newline being deleted
                        const formatDelta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]()// @ts-expect-error Fix me later
                        .retain(range.index + line.length() - 2).retain(1, formats);
                        delta = delta.compose(formatDelta);
                    }
                }
            }
        }
        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.quill.focus();
    }
    handleDelete(range, context) {
        // Check for astral symbols
        const length = /^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(context.suffix) ? 2 : 1;
        if (range.index >= this.quill.getLength() - length) return;
        let formats = {};
        const [line] = this.quill.getLine(range.index);
        let delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).delete(length);
        // @ts-expect-error Fix me later
        if (context.offset >= line.length() - 1) {
            const [next] = this.quill.getLine(range.index + 1);
            if (next) {
                // @ts-expect-error Fix me later
                const curFormats = line.formats();
                const nextFormats = this.quill.getFormat(range.index, 1);
                formats = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(curFormats, nextFormats) || {};
                if (Object.keys(formats).length > 0) {
                    delta = delta.retain(next.length() - 1).retain(1, formats);
                }
            }
        }
        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.quill.focus();
    }
    handleDeleteRange(range) {
        deleteRange({
            range,
            quill: this.quill
        });
        this.quill.focus();
    }
    handleEnter(range, context) {
        const lineFormats = Object.keys(context.format).reduce((formats, format)=>{
            if (this.quill.scroll.query(format, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK) && !Array.isArray(context.format[format])) {
                formats[format] = context.format[format];
            }
            return formats;
        }, {});
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).delete(range.length).insert('\n', lineFormats);
        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.quill.setSelection(range.index + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        this.quill.focus();
    }
}
const defaultOptions = {
    bindings: {
        bold: makeFormatHandler('bold'),
        italic: makeFormatHandler('italic'),
        underline: makeFormatHandler('underline'),
        indent: {
            // highlight tab or tab at beginning of list, indent or blockquote
            key: 'Tab',
            format: [
                'blockquote',
                'indent',
                'list'
            ],
            handler (range, context) {
                if (context.collapsed && context.offset !== 0) return true;
                this.quill.format('indent', '+1', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                return false;
            }
        },
        outdent: {
            key: 'Tab',
            shiftKey: true,
            format: [
                'blockquote',
                'indent',
                'list'
            ],
            // highlight tab or tab at beginning of list, indent or blockquote
            handler (range, context) {
                if (context.collapsed && context.offset !== 0) return true;
                this.quill.format('indent', '-1', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                return false;
            }
        },
        'outdent backspace': {
            key: 'Backspace',
            collapsed: true,
            shiftKey: null,
            metaKey: null,
            ctrlKey: null,
            altKey: null,
            format: [
                'indent',
                'list'
            ],
            offset: 0,
            handler (range, context) {
                if (context.format.indent != null) {
                    this.quill.format('indent', '-1', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                } else if (context.format.list != null) {
                    this.quill.format('list', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                }
            }
        },
        'indent code-block': makeCodeBlockHandler(true),
        'outdent code-block': makeCodeBlockHandler(false),
        'remove tab': {
            key: 'Tab',
            shiftKey: true,
            collapsed: true,
            prefix: /\t$/,
            handler (range) {
                this.quill.deleteText(range.index - 1, 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        },
        tab: {
            key: 'Tab',
            handler (range, context) {
                if (context.format.table) return true;
                this.quill.history.cutoff();
                const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).delete(range.length).insert('\t');
                this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.history.cutoff();
                this.quill.setSelection(range.index + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                return false;
            }
        },
        'blockquote empty enter': {
            key: 'Enter',
            collapsed: true,
            format: [
                'blockquote'
            ],
            empty: true,
            handler () {
                this.quill.format('blockquote', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        },
        'list empty enter': {
            key: 'Enter',
            collapsed: true,
            format: [
                'list'
            ],
            empty: true,
            handler (range, context) {
                const formats = {
                    list: false
                };
                if (context.format.indent) {
                    formats.indent = false;
                }
                this.quill.formatLine(range.index, range.length, formats, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        },
        'checklist enter': {
            key: 'Enter',
            collapsed: true,
            format: {
                list: 'checked'
            },
            handler (range) {
                const [line, offset] = this.quill.getLine(range.index);
                const formats = {
                    // @ts-expect-error Fix me later
                    ...line.formats(),
                    list: 'checked'
                };
                const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).insert('\n', formats)// @ts-expect-error Fix me later
                .retain(line.length() - offset - 1).retain(1, {
                    list: 'unchecked'
                });
                this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.setSelection(range.index + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                this.quill.scrollSelectionIntoView();
            }
        },
        'header enter': {
            key: 'Enter',
            collapsed: true,
            format: [
                'header'
            ],
            suffix: /^$/,
            handler (range, context) {
                const [line, offset] = this.quill.getLine(range.index);
                const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).insert('\n', context.format)// @ts-expect-error Fix me later
                .retain(line.length() - offset - 1).retain(1, {
                    header: null
                });
                this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.setSelection(range.index + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                this.quill.scrollSelectionIntoView();
            }
        },
        'table backspace': {
            key: 'Backspace',
            format: [
                'table'
            ],
            collapsed: true,
            offset: 0,
            handler () {}
        },
        'table delete': {
            key: 'Delete',
            format: [
                'table'
            ],
            collapsed: true,
            suffix: /^$/,
            handler () {}
        },
        'table enter': {
            key: 'Enter',
            shiftKey: null,
            format: [
                'table'
            ],
            handler (range) {
                const module = this.quill.getModule('table');
                if (module) {
                    // @ts-expect-error
                    const [table, row, cell, offset] = module.getTable(range);
                    const shift = tableSide(table, row, cell, offset);
                    if (shift == null) return;
                    let index = table.offset();
                    if (shift < 0) {
                        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).insert('\n');
                        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                        this.quill.setSelection(range.index + 1, range.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                    } else if (shift > 0) {
                        index += table.length();
                        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).insert('\n');
                        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                        this.quill.setSelection(index, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    }
                }
            }
        },
        'table tab': {
            key: 'Tab',
            shiftKey: null,
            format: [
                'table'
            ],
            handler (range, context) {
                const { event, line: cell } = context;
                const offset = cell.offset(this.quill.scroll);
                if (event.shiftKey) {
                    this.quill.setSelection(offset - 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                } else {
                    this.quill.setSelection(offset + cell.length(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                }
            }
        },
        'list autofill': {
            key: ' ',
            shiftKey: null,
            collapsed: true,
            format: {
                'code-block': false,
                blockquote: false,
                table: false
            },
            prefix: /^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,
            handler (range, context) {
                if (this.quill.scroll.query('list') == null) return true;
                const { length } = context.prefix;
                const [line, offset] = this.quill.getLine(range.index);
                if (offset > length) return true;
                let value;
                switch(context.prefix.trim()){
                    case '[]':
                    case '[ ]':
                        value = 'unchecked';
                        break;
                    case '[x]':
                        value = 'checked';
                        break;
                    case '-':
                    case '*':
                        value = 'bullet';
                        break;
                    default:
                        value = 'ordered';
                }
                this.quill.insertText(range.index, ' ', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.history.cutoff();
                const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index - offset).delete(length + 1)// @ts-expect-error Fix me later
                .retain(line.length() - 2 - offset).retain(1, {
                    list: value
                });
                this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.history.cutoff();
                this.quill.setSelection(range.index - length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                return false;
            }
        },
        'code exit': {
            key: 'Enter',
            collapsed: true,
            format: [
                'code-block'
            ],
            prefix: /^$/,
            suffix: /^\s*$/,
            handler (range) {
                const [line, offset] = this.quill.getLine(range.index);
                let numLines = 2;
                let cur = line;
                while(cur != null && cur.length() <= 1 && cur.formats()['code-block']){
                    // @ts-expect-error
                    cur = cur.prev;
                    numLines -= 1;
                    // Requisite prev lines are empty
                    if (numLines <= 0) {
                        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]()// @ts-expect-error Fix me later
                        .retain(range.index + line.length() - offset - 2).retain(1, {
                            'code-block': null
                        }).delete(1);
                        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                        this.quill.setSelection(range.index - 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                        return false;
                    }
                }
                return true;
            }
        },
        'embed left': makeEmbedArrowHandler('ArrowLeft', false),
        'embed left shift': makeEmbedArrowHandler('ArrowLeft', true),
        'embed right': makeEmbedArrowHandler('ArrowRight', false),
        'embed right shift': makeEmbedArrowHandler('ArrowRight', true),
        'table down': makeTableArrowHandler(false),
        'table up': makeTableArrowHandler(true)
    }
};
Keyboard.DEFAULTS = defaultOptions;
function makeCodeBlockHandler(indent) {
    return {
        key: 'Tab',
        shiftKey: !indent,
        format: {
            'code-block': true
        },
        handler (range, _ref) {
            let { event } = _ref;
            const CodeBlock = this.quill.scroll.query('code-block');
            // @ts-expect-error
            const { TAB } = CodeBlock;
            if (range.length === 0 && !event.shiftKey) {
                this.quill.insertText(range.index, TAB, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                this.quill.setSelection(range.index + TAB.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
                return;
            }
            const lines = range.length === 0 ? this.quill.getLines(range.index, 1) : this.quill.getLines(range);
            let { index, length } = range;
            lines.forEach((line, i)=>{
                if (indent) {
                    line.insertAt(0, TAB);
                    if (i === 0) {
                        index += TAB.length;
                    } else {
                        length += TAB.length;
                    }
                // @ts-expect-error Fix me later
                } else if (line.domNode.textContent.startsWith(TAB)) {
                    line.deleteAt(0, TAB.length);
                    if (i === 0) {
                        index -= TAB.length;
                    } else {
                        length -= TAB.length;
                    }
                }
            });
            this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            this.quill.setSelection(index, length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        }
    };
}
function makeEmbedArrowHandler(key, shiftKey) {
    const where = key === 'ArrowLeft' ? 'prefix' : 'suffix';
    return {
        key,
        shiftKey,
        altKey: null,
        [where]: /^$/,
        handler (range) {
            let { index } = range;
            if (key === 'ArrowRight') {
                index += range.length + 1;
            }
            const [leaf] = this.quill.getLeaf(index);
            if (!(leaf instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"])) return true;
            if (key === 'ArrowLeft') {
                if (shiftKey) {
                    this.quill.setSelection(range.index - 1, range.length + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                } else {
                    this.quill.setSelection(range.index - 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                }
            } else if (shiftKey) {
                this.quill.setSelection(range.index, range.length + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            } else {
                this.quill.setSelection(range.index + range.length + 1, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
            return false;
        }
    };
}
function makeFormatHandler(format) {
    return {
        key: format[0],
        shortKey: true,
        handler (range, context) {
            this.quill.format(format, !context.format[format], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        }
    };
}
function makeTableArrowHandler(up) {
    return {
        key: up ? 'ArrowUp' : 'ArrowDown',
        collapsed: true,
        format: [
            'table'
        ],
        handler (range, context) {
            // TODO move to table module
            const key = up ? 'prev' : 'next';
            const cell = context.line;
            const targetRow = cell.parent[key];
            if (targetRow != null) {
                if (targetRow.statics.blotName === 'table-row') {
                    // @ts-expect-error
                    let targetCell = targetRow.children.head;
                    let cur = cell;
                    while(cur.prev != null){
                        // @ts-expect-error
                        cur = cur.prev;
                        targetCell = targetCell.next;
                    }
                    const index = targetCell.offset(this.quill.scroll) + Math.min(context.offset, targetCell.length() - 1);
                    this.quill.setSelection(index, 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                }
            } else {
                // @ts-expect-error
                const targetLine = cell.table()[key];
                if (targetLine != null) {
                    if (up) {
                        this.quill.setSelection(targetLine.offset(this.quill.scroll) + targetLine.length() - 1, 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    } else {
                        this.quill.setSelection(targetLine.offset(this.quill.scroll), 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    }
                }
            }
            return false;
        }
    };
}
function normalize(binding) {
    if (typeof binding === 'string' || typeof binding === 'number') {
        binding = {
            key: binding
        };
    } else if (typeof binding === 'object') {
        binding = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$cloneDeep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__cloneDeep$3e$__["cloneDeep"])(binding);
    } else {
        return null;
    }
    if (binding.shortKey) {
        binding[SHORTKEY] = binding.shortKey;
        delete binding.shortKey;
    }
    return binding;
}
// TODO: Move into quill.ts or editor.ts
function deleteRange(_ref2) {
    let { quill, range } = _ref2;
    const lines = quill.getLines(range);
    let formats = {};
    if (lines.length > 1) {
        const firstFormats = lines[0].formats();
        const lastFormats = lines[lines.length - 1].formats();
        formats = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AttributeMap"].diff(lastFormats, firstFormats) || {};
    }
    quill.deleteText(range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
    if (Object.keys(formats).length > 0) {
        quill.formatLine(range.index, 1, formats, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
    }
    quill.setSelection(range.index, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
}
function tableSide(_table, row, cell, offset) {
    if (row.prev == null && row.next == null) {
        if (cell.prev == null && cell.next == null) {
            return offset === 0 ? -1 : 1;
        }
        return cell.prev == null ? -1 : 1;
    }
    if (row.prev == null) {
        return -1;
    }
    if (row.next == null) {
        return 1;
    }
    return null;
}
;
 //# sourceMappingURL=keyboard.js.map
}}),
"[project]/node_modules/quill/modules/normalizeExternalHTML/normalizers/googleDocs.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>normalize)
});
const normalWeightRegexp = /font-weight:\s*normal/;
const blockTagNames = [
    'P',
    'OL',
    'UL'
];
const isBlockElement = (element)=>{
    return element && blockTagNames.includes(element.tagName);
};
const normalizeEmptyLines = (doc)=>{
    Array.from(doc.querySelectorAll('br')).filter((br)=>isBlockElement(br.previousElementSibling) && isBlockElement(br.nextElementSibling)).forEach((br)=>{
        br.parentNode?.removeChild(br);
    });
};
const normalizeFontWeight = (doc)=>{
    Array.from(doc.querySelectorAll('b[style*="font-weight"]')).filter((node)=>node.getAttribute('style')?.match(normalWeightRegexp)).forEach((node)=>{
        const fragment = doc.createDocumentFragment();
        fragment.append(...node.childNodes);
        node.parentNode?.replaceChild(fragment, node);
    });
};
function normalize(doc) {
    if (doc.querySelector('[id^="docs-internal-guid-"]')) {
        normalizeFontWeight(doc);
        normalizeEmptyLines(doc);
    }
} //# sourceMappingURL=googleDocs.js.map
}}),
"[project]/node_modules/quill/modules/normalizeExternalHTML/normalizers/msWord.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>normalize)
});
const ignoreRegexp = /\bmso-list:[^;]*ignore/i;
const idRegexp = /\bmso-list:[^;]*\bl(\d+)/i;
const indentRegexp = /\bmso-list:[^;]*\blevel(\d+)/i;
const parseListItem = (element, html)=>{
    const style = element.getAttribute('style');
    const idMatch = style?.match(idRegexp);
    if (!idMatch) {
        return null;
    }
    const id = Number(idMatch[1]);
    const indentMatch = style?.match(indentRegexp);
    const indent = indentMatch ? Number(indentMatch[1]) : 1;
    const typeRegexp = new RegExp(`@list l${id}:level${indent}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`, 'i');
    const typeMatch = html.match(typeRegexp);
    const type = typeMatch && typeMatch[1] === 'bullet' ? 'bullet' : 'ordered';
    return {
        id,
        indent,
        type,
        element
    };
};
// list items are represented as `p` tags with styles like `mso-list: l0 level1` where:
// 1. "0" in "l0" means the list item id;
// 2. "1" in "level1" means the indent level, starting from 1.
const normalizeListItem = (doc)=>{
    const msoList = Array.from(doc.querySelectorAll('[style*=mso-list]'));
    const ignored = [];
    const others = [];
    msoList.forEach((node)=>{
        const shouldIgnore = (node.getAttribute('style') || '').match(ignoreRegexp);
        if (shouldIgnore) {
            ignored.push(node);
        } else {
            others.push(node);
        }
    });
    // Each list item contains a marker wrapped with "mso-list: Ignore".
    ignored.forEach((node)=>node.parentNode?.removeChild(node));
    // The list stype is not defined inline with the tag, instead, it's in the
    // style tag so we need to pass the html as a string.
    const html = doc.documentElement.innerHTML;
    const listItems = others.map((element)=>parseListItem(element, html)).filter((parsed)=>parsed);
    while(listItems.length){
        const childListItems = [];
        let current = listItems.shift();
        // Group continuous items into the same group (aka "ul")
        while(current){
            childListItems.push(current);
            current = listItems.length && listItems[0]?.element === current.element.nextElementSibling && // Different id means the next item doesn't belong to this group.
            listItems[0].id === current.id ? listItems.shift() : null;
        }
        const ul = document.createElement('ul');
        childListItems.forEach((listItem)=>{
            const li = document.createElement('li');
            li.setAttribute('data-list', listItem.type);
            if (listItem.indent > 1) {
                li.setAttribute('class', `ql-indent-${listItem.indent - 1}`);
            }
            li.innerHTML = listItem.element.innerHTML;
            ul.appendChild(li);
        });
        const element = childListItems[0]?.element;
        const { parentNode } = element ?? {};
        if (element) {
            parentNode?.replaceChild(ul, element);
        }
        childListItems.slice(1).forEach((_ref)=>{
            let { element: e } = _ref;
            parentNode?.removeChild(e);
        });
    }
};
function normalize(doc) {
    if (doc.documentElement.getAttribute('xmlns:w') === 'urn:schemas-microsoft-com:office:word') {
        normalizeListItem(doc);
    }
} //# sourceMappingURL=msWord.js.map
}}),
"[project]/node_modules/quill/modules/normalizeExternalHTML/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$normalizers$2f$googleDocs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/normalizeExternalHTML/normalizers/googleDocs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$normalizers$2f$msWord$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/normalizeExternalHTML/normalizers/msWord.js [app-ssr] (ecmascript)");
;
;
const NORMALIZERS = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$normalizers$2f$msWord$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$normalizers$2f$googleDocs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
const normalizeExternalHTML = (doc)=>{
    if (doc.documentElement) {
        NORMALIZERS.forEach((normalize)=>{
            normalize(doc);
        });
    }
};
const __TURBOPACK__default__export__ = normalizeExternalHTML;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/quill/modules/clipboard.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Clipboard),
    "matchAttributor": (()=>matchAttributor),
    "matchBlot": (()=>matchBlot),
    "matchNewline": (()=>matchNewline),
    "matchText": (()=>matchText),
    "traverse": (()=>traverse)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/align.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/background.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/direction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/font.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/keyboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/normalizeExternalHTML/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill:clipboard');
const CLIPBOARD_CONFIG = [
    [
        Node.TEXT_NODE,
        matchText
    ],
    [
        Node.TEXT_NODE,
        matchNewline
    ],
    [
        'br',
        matchBreak
    ],
    [
        Node.ELEMENT_NODE,
        matchNewline
    ],
    [
        Node.ELEMENT_NODE,
        matchBlot
    ],
    [
        Node.ELEMENT_NODE,
        matchAttributor
    ],
    [
        Node.ELEMENT_NODE,
        matchStyles
    ],
    [
        'li',
        matchIndent
    ],
    [
        'ol, ul',
        matchList
    ],
    [
        'pre',
        matchCodeBlock
    ],
    [
        'tr',
        matchTable
    ],
    [
        'b',
        createMatchAlias('bold')
    ],
    [
        'i',
        createMatchAlias('italic')
    ],
    [
        'strike',
        createMatchAlias('strike')
    ],
    [
        'style',
        matchIgnore
    ]
];
const ATTRIBUTE_ATTRIBUTORS = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlignAttribute"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionAttribute"]
].reduce((memo, attr)=>{
    memo[attr.keyName] = attr;
    return memo;
}, {});
const STYLE_ATTRIBUTORS = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlignStyle"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BackgroundStyle"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorStyle"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionStyle"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontStyle"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SizeStyle"]
].reduce((memo, attr)=>{
    memo[attr.keyName] = attr;
    return memo;
}, {});
class Clipboard extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static DEFAULTS = {
        matchers: []
    };
    constructor(quill, options){
        super(quill, options);
        this.quill.root.addEventListener('copy', (e)=>this.onCaptureCopy(e, false));
        this.quill.root.addEventListener('cut', (e)=>this.onCaptureCopy(e, true));
        this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));
        this.matchers = [];
        CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach((_ref)=>{
            let [selector, matcher] = _ref;
            this.addMatcher(selector, matcher);
        });
    }
    addMatcher(selector, matcher) {
        this.matchers.push([
            selector,
            matcher
        ]);
    }
    convert(_ref2) {
        let { html, text } = _ref2;
        let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        if (formats[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName]) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(text || '', {
                [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName]: formats[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName]
            });
        }
        if (!html) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(text || '', formats);
        }
        const delta = this.convertHTML(html);
        // Remove trailing newline
        if (deltaEndsWith(delta, '\n') && (delta.ops[delta.ops.length - 1].attributes == null || formats.table)) {
            return delta.compose(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(delta.length() - 1).delete(1));
        }
        return delta;
    }
    normalizeHTML(doc) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$normalizeExternalHTML$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(doc);
    }
    convertHTML(html) {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        this.normalizeHTML(doc);
        const container = doc.body;
        const nodeMatches = new WeakMap();
        const [elementMatchers, textMatchers] = this.prepareMatching(container, nodeMatches);
        return traverse(this.quill.scroll, container, elementMatchers, textMatchers, nodeMatches);
    }
    dangerouslyPasteHTML(index, html) {
        let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.API;
        if (typeof index === 'string') {
            const delta = this.convert({
                html: index,
                text: ''
            });
            // @ts-expect-error
            this.quill.setContents(delta, html);
            this.quill.setSelection(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        } else {
            const paste = this.convert({
                html,
                text: ''
            });
            this.quill.updateContents(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(index).concat(paste), source);
            this.quill.setSelection(index + paste.length(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        }
    }
    onCaptureCopy(e) {
        let isCut = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        if (e.defaultPrevented) return;
        e.preventDefault();
        const [range] = this.quill.selection.getRange();
        if (range == null) return;
        const { html, text } = this.onCopy(range, isCut);
        e.clipboardData?.setData('text/plain', text);
        e.clipboardData?.setData('text/html', html);
        if (isCut) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteRange"])({
                range,
                quill: this.quill
            });
        }
    }
    /*
   * https://www.iana.org/assignments/media-types/text/uri-list
   */ normalizeURIList(urlList) {
        return urlList.split(/\r?\n/)// Ignore all comments
        .filter((url)=>url[0] !== '#').join('\n');
    }
    onCapturePaste(e) {
        if (e.defaultPrevented || !this.quill.isEnabled()) return;
        e.preventDefault();
        const range = this.quill.getSelection(true);
        if (range == null) return;
        const html = e.clipboardData?.getData('text/html');
        let text = e.clipboardData?.getData('text/plain');
        if (!html && !text) {
            const urlList = e.clipboardData?.getData('text/uri-list');
            if (urlList) {
                text = this.normalizeURIList(urlList);
            }
        }
        const files = Array.from(e.clipboardData?.files || []);
        if (!html && files.length > 0) {
            this.quill.uploader.upload(range, files);
            return;
        }
        if (html && files.length > 0) {
            const doc = new DOMParser().parseFromString(html, 'text/html');
            if (doc.body.childElementCount === 1 && doc.body.firstElementChild?.tagName === 'IMG') {
                this.quill.uploader.upload(range, files);
                return;
            }
        }
        this.onPaste(range, {
            html,
            text
        });
    }
    onCopy(range) {
        const text = this.quill.getText(range);
        const html = this.quill.getSemanticHTML(range);
        return {
            html,
            text
        };
    }
    onPaste(range, _ref3) {
        let { text, html } = _ref3;
        const formats = this.quill.getFormat(range.index);
        const pastedDelta = this.convert({
            text,
            html
        }, formats);
        debug.log('onPaste', pastedDelta, {
            text,
            html
        });
        const delta = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).delete(range.length).concat(pastedDelta);
        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        // range.length contributes to delta.length()
        this.quill.setSelection(delta.length() - range.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        this.quill.scrollSelectionIntoView();
    }
    prepareMatching(container, nodeMatches) {
        const elementMatchers = [];
        const textMatchers = [];
        this.matchers.forEach((pair)=>{
            const [selector, matcher] = pair;
            switch(selector){
                case Node.TEXT_NODE:
                    textMatchers.push(matcher);
                    break;
                case Node.ELEMENT_NODE:
                    elementMatchers.push(matcher);
                    break;
                default:
                    Array.from(container.querySelectorAll(selector)).forEach((node)=>{
                        if (nodeMatches.has(node)) {
                            const matches = nodeMatches.get(node);
                            matches?.push(matcher);
                        } else {
                            nodeMatches.set(node, [
                                matcher
                            ]);
                        }
                    });
                    break;
            }
        });
        return [
            elementMatchers,
            textMatchers
        ];
    }
}
function applyFormat(delta, format, value, scroll) {
    if (!scroll.query(format)) {
        return delta;
    }
    return delta.reduce((newDelta, op)=>{
        if (!op.insert) return newDelta;
        if (op.attributes && op.attributes[format]) {
            return newDelta.push(op);
        }
        const formats = value ? {
            [format]: value
        } : {};
        return newDelta.insert(op.insert, {
            ...formats,
            ...op.attributes
        });
    }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
}
function deltaEndsWith(delta, text) {
    let endText = '';
    for(let i = delta.ops.length - 1; i >= 0 && endText.length < text.length; --i // eslint-disable-line no-plusplus
    ){
        const op = delta.ops[i];
        if (typeof op.insert !== 'string') break;
        endText = op.insert + endText;
    }
    return endText.slice(-1 * text.length) === text;
}
function isLine(node, scroll) {
    if (!(node instanceof Element)) return false;
    const match = scroll.query(node);
    // @ts-expect-error
    if (match && match.prototype instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"]) return false;
    return [
        'address',
        'article',
        'blockquote',
        'canvas',
        'dd',
        'div',
        'dl',
        'dt',
        'fieldset',
        'figcaption',
        'figure',
        'footer',
        'form',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'header',
        'iframe',
        'li',
        'main',
        'nav',
        'ol',
        'output',
        'p',
        'pre',
        'section',
        'table',
        'td',
        'tr',
        'ul',
        'video'
    ].includes(node.tagName.toLowerCase());
}
function isBetweenInlineElements(node, scroll) {
    return node.previousElementSibling && node.nextElementSibling && !isLine(node.previousElementSibling, scroll) && !isLine(node.nextElementSibling, scroll);
}
const preNodes = new WeakMap();
function isPre(node) {
    if (node == null) return false;
    if (!preNodes.has(node)) {
        // @ts-expect-error
        if (node.tagName === 'PRE') {
            preNodes.set(node, true);
        } else {
            preNodes.set(node, isPre(node.parentNode));
        }
    }
    return preNodes.get(node);
}
function traverse(scroll, node, elementMatchers, textMatchers, nodeMatches) {
    // Post-order
    if (node.nodeType === node.TEXT_NODE) {
        return textMatchers.reduce((delta, matcher)=>{
            return matcher(node, delta, scroll);
        }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
    }
    if (node.nodeType === node.ELEMENT_NODE) {
        return Array.from(node.childNodes || []).reduce((delta, childNode)=>{
            let childrenDelta = traverse(scroll, childNode, elementMatchers, textMatchers, nodeMatches);
            if (childNode.nodeType === node.ELEMENT_NODE) {
                childrenDelta = elementMatchers.reduce((reducedDelta, matcher)=>{
                    return matcher(childNode, reducedDelta, scroll);
                }, childrenDelta);
                childrenDelta = (nodeMatches.get(childNode) || []).reduce((reducedDelta, matcher)=>{
                    return matcher(childNode, reducedDelta, scroll);
                }, childrenDelta);
            }
            return delta.concat(childrenDelta);
        }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
}
function createMatchAlias(format) {
    return (_node, delta, scroll)=>{
        return applyFormat(delta, format, true, scroll);
    };
}
function matchAttributor(node, delta, scroll) {
    const attributes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Attributor"].keys(node);
    const classes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"].keys(node);
    const styles = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StyleAttributor"].keys(node);
    const formats = {};
    attributes.concat(classes).concat(styles).forEach((name)=>{
        let attr = scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].ATTRIBUTE);
        if (attr != null) {
            formats[attr.attrName] = attr.value(node);
            if (formats[attr.attrName]) return;
        }
        attr = ATTRIBUTE_ATTRIBUTORS[name];
        if (attr != null && (attr.attrName === name || attr.keyName === name)) {
            formats[attr.attrName] = attr.value(node) || undefined;
        }
        attr = STYLE_ATTRIBUTORS[name];
        if (attr != null && (attr.attrName === name || attr.keyName === name)) {
            attr = STYLE_ATTRIBUTORS[name];
            formats[attr.attrName] = attr.value(node) || undefined;
        }
    });
    return Object.entries(formats).reduce((newDelta, _ref4)=>{
        let [name, value] = _ref4;
        return applyFormat(newDelta, name, value, scroll);
    }, delta);
}
function matchBlot(node, delta, scroll) {
    const match = scroll.query(node);
    if (match == null) return delta;
    // @ts-expect-error
    if (match.prototype instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"]) {
        const embed = {};
        // @ts-expect-error
        const value = match.value(node);
        if (value != null) {
            // @ts-expect-error
            embed[match.blotName] = value;
            // @ts-expect-error
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert(embed, match.formats(node, scroll));
        }
    } else {
        // @ts-expect-error
        if (match.prototype instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockBlot"] && !deltaEndsWith(delta, '\n')) {
            delta.insert('\n');
        }
        if ('blotName' in match && 'formats' in match && typeof match.formats === 'function') {
            return applyFormat(delta, match.blotName, match.formats(node, scroll), scroll);
        }
    }
    return delta;
}
function matchBreak(node, delta) {
    if (!deltaEndsWith(delta, '\n')) {
        delta.insert('\n');
    }
    return delta;
}
function matchCodeBlock(node, delta, scroll) {
    const match = scroll.query('code-block');
    const language = match && 'formats' in match && typeof match.formats === 'function' ? match.formats(node, scroll) : true;
    return applyFormat(delta, 'code-block', language, scroll);
}
function matchIgnore() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
}
function matchIndent(node, delta, scroll) {
    const match = scroll.query(node);
    if (match == null || // @ts-expect-error
    match.blotName !== 'list' || !deltaEndsWith(delta, '\n')) {
        return delta;
    }
    let indent = -1;
    let parent = node.parentNode;
    while(parent != null){
        // @ts-expect-error
        if ([
            'OL',
            'UL'
        ].includes(parent.tagName)) {
            indent += 1;
        }
        parent = parent.parentNode;
    }
    if (indent <= 0) return delta;
    return delta.reduce((composed, op)=>{
        if (!op.insert) return composed;
        if (op.attributes && typeof op.attributes.indent === 'number') {
            return composed.push(op);
        }
        return composed.insert(op.insert, {
            indent,
            ...op.attributes || {}
        });
    }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
}
function matchList(node, delta, scroll) {
    const element = node;
    let list = element.tagName === 'OL' ? 'ordered' : 'bullet';
    const checkedAttr = element.getAttribute('data-checked');
    if (checkedAttr) {
        list = checkedAttr === 'true' ? 'checked' : 'unchecked';
    }
    return applyFormat(delta, 'list', list, scroll);
}
function matchNewline(node, delta, scroll) {
    if (!deltaEndsWith(delta, '\n')) {
        if (isLine(node, scroll) && (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)) {
            return delta.insert('\n');
        }
        if (delta.length() > 0 && node.nextSibling) {
            let nextSibling = node.nextSibling;
            while(nextSibling != null){
                if (isLine(nextSibling, scroll)) {
                    return delta.insert('\n');
                }
                const match = scroll.query(nextSibling);
                // @ts-expect-error
                if (match && match.prototype instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"]) {
                    return delta.insert('\n');
                }
                nextSibling = nextSibling.firstChild;
            }
        }
    }
    return delta;
}
function matchStyles(node, delta, scroll) {
    const formats = {};
    const style = node.style || {};
    if (style.fontStyle === 'italic') {
        formats.italic = true;
    }
    if (style.textDecoration === 'underline') {
        formats.underline = true;
    }
    if (style.textDecoration === 'line-through') {
        formats.strike = true;
    }
    if (style.fontWeight?.startsWith('bold') || // @ts-expect-error Fix me later
    parseInt(style.fontWeight, 10) >= 700) {
        formats.bold = true;
    }
    delta = Object.entries(formats).reduce((newDelta, _ref5)=>{
        let [name, value] = _ref5;
        return applyFormat(newDelta, name, value, scroll);
    }, delta);
    // @ts-expect-error
    if (parseFloat(style.textIndent || 0) > 0) {
        // Could be 0.5in
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().insert('\t').concat(delta);
    }
    return delta;
}
function matchTable(node, delta, scroll) {
    const table = node.parentElement?.tagName === 'TABLE' ? node.parentElement : node.parentElement?.parentElement;
    if (table != null) {
        const rows = Array.from(table.querySelectorAll('tr'));
        const row = rows.indexOf(node) + 1;
        return applyFormat(delta, 'table', row, scroll);
    }
    return delta;
}
function matchText(node, delta, scroll) {
    // @ts-expect-error
    let text = node.data;
    // Word represents empty line with <o:p>&nbsp;</o:p>
    if (node.parentElement?.tagName === 'O:P') {
        return delta.insert(text.trim());
    }
    if (!isPre(node)) {
        if (text.trim().length === 0 && text.includes('\n') && !isBetweenInlineElements(node, scroll)) {
            return delta;
        }
        // convert all non-nbsp whitespace into regular space
        text = text.replace(/[^\S\u00a0]/g, ' ');
        // collapse consecutive spaces into one
        text = text.replace(/ {2,}/g, ' ');
        if (node.previousSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.previousSibling instanceof Element && isLine(node.previousSibling, scroll)) {
            // block structure means we don't need leading space
            text = text.replace(/^ /, '');
        }
        if (node.nextSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.nextSibling instanceof Element && isLine(node.nextSibling, scroll)) {
            // block structure means we don't need trailing space
            text = text.replace(/ $/, '');
        }
        // done removing whitespace and can normalize all to regular space
        text = text.replaceAll('\u00a0', ' ');
    }
    return delta.insert(text);
}
;
 //# sourceMappingURL=clipboard.js.map
}}),
"[project]/node_modules/quill/modules/history.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>History),
    "getLastChangeIndex": (()=>getLastChangeIndex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
class History extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static DEFAULTS = {
        delay: 1000,
        maxStack: 100,
        userOnly: false
    };
    lastRecorded = 0;
    ignoreChange = false;
    stack = {
        undo: [],
        redo: []
    };
    currentRange = null;
    constructor(quill, options){
        super(quill, options);
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.EDITOR_CHANGE, (eventName, value, oldValue, source)=>{
            if (eventName === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.SELECTION_CHANGE) {
                if (value && source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT) {
                    this.currentRange = value;
                }
            } else if (eventName === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.TEXT_CHANGE) {
                if (!this.ignoreChange) {
                    if (!this.options.userOnly || source === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER) {
                        this.record(value, oldValue);
                    } else {
                        this.transform(value);
                    }
                }
                this.currentRange = transformRange(this.currentRange, value);
            }
        });
        this.quill.keyboard.addBinding({
            key: 'z',
            shortKey: true
        }, this.undo.bind(this));
        this.quill.keyboard.addBinding({
            key: [
                'z',
                'Z'
            ],
            shortKey: true,
            shiftKey: true
        }, this.redo.bind(this));
        if (/Win/i.test(navigator.platform)) {
            this.quill.keyboard.addBinding({
                key: 'y',
                shortKey: true
            }, this.redo.bind(this));
        }
        this.quill.root.addEventListener('beforeinput', (event)=>{
            if (event.inputType === 'historyUndo') {
                this.undo();
                event.preventDefault();
            } else if (event.inputType === 'historyRedo') {
                this.redo();
                event.preventDefault();
            }
        });
    }
    change(source, dest) {
        if (this.stack[source].length === 0) return;
        const item = this.stack[source].pop();
        if (!item) return;
        const base = this.quill.getContents();
        const inverseDelta = item.delta.invert(base);
        this.stack[dest].push({
            delta: inverseDelta,
            range: transformRange(item.range, inverseDelta)
        });
        this.lastRecorded = 0;
        this.ignoreChange = true;
        this.quill.updateContents(item.delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.ignoreChange = false;
        this.restoreSelection(item);
    }
    clear() {
        this.stack = {
            undo: [],
            redo: []
        };
    }
    cutoff() {
        this.lastRecorded = 0;
    }
    record(changeDelta, oldDelta) {
        if (changeDelta.ops.length === 0) return;
        this.stack.redo = [];
        let undoDelta = changeDelta.invert(oldDelta);
        let undoRange = this.currentRange;
        const timestamp = Date.now();
        if (// @ts-expect-error Fix me later
        this.lastRecorded + this.options.delay > timestamp && this.stack.undo.length > 0) {
            const item = this.stack.undo.pop();
            if (item) {
                undoDelta = undoDelta.compose(item.delta);
                undoRange = item.range;
            }
        } else {
            this.lastRecorded = timestamp;
        }
        if (undoDelta.length() === 0) return;
        this.stack.undo.push({
            delta: undoDelta,
            range: undoRange
        });
        // @ts-expect-error Fix me later
        if (this.stack.undo.length > this.options.maxStack) {
            this.stack.undo.shift();
        }
    }
    redo() {
        this.change('redo', 'undo');
    }
    transform(delta) {
        transformStack(this.stack.undo, delta);
        transformStack(this.stack.redo, delta);
    }
    undo() {
        this.change('undo', 'redo');
    }
    restoreSelection(stackItem) {
        if (stackItem.range) {
            this.quill.setSelection(stackItem.range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        } else {
            const index = getLastChangeIndex(this.quill.scroll, stackItem.delta);
            this.quill.setSelection(index, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        }
    }
}
function transformStack(stack, delta) {
    let remoteDelta = delta;
    for(let i = stack.length - 1; i >= 0; i -= 1){
        const oldItem = stack[i];
        stack[i] = {
            delta: remoteDelta.transform(oldItem.delta, true),
            range: oldItem.range && transformRange(oldItem.range, remoteDelta)
        };
        remoteDelta = oldItem.delta.transform(remoteDelta);
        if (stack[i].delta.length() === 0) {
            stack.splice(i, 1);
        }
    }
}
function endsWithNewlineChange(scroll, delta) {
    const lastOp = delta.ops[delta.ops.length - 1];
    if (lastOp == null) return false;
    if (lastOp.insert != null) {
        return typeof lastOp.insert === 'string' && lastOp.insert.endsWith('\n');
    }
    if (lastOp.attributes != null) {
        return Object.keys(lastOp.attributes).some((attr)=>{
            return scroll.query(attr, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK) != null;
        });
    }
    return false;
}
function getLastChangeIndex(scroll, delta) {
    const deleteLength = delta.reduce((length, op)=>{
        return length + (op.delete || 0);
    }, 0);
    let changeIndex = delta.length() - deleteLength;
    if (endsWithNewlineChange(scroll, delta)) {
        changeIndex -= 1;
    }
    return changeIndex;
}
function transformRange(range, delta) {
    if (!range) return range;
    const start = delta.transformPosition(range.index);
    const end = delta.transformPosition(range.index + range.length);
    return {
        index: start,
        length: end - start
    };
}
;
 //# sourceMappingURL=history.js.map
}}),
"[project]/node_modules/quill/modules/uploader.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
;
;
;
class Uploader extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        super(quill, options);
        quill.root.addEventListener('drop', (e)=>{
            e.preventDefault();
            let native = null;
            if (document.caretRangeFromPoint) {
                native = document.caretRangeFromPoint(e.clientX, e.clientY);
            // @ts-expect-error
            } else if (document.caretPositionFromPoint) {
                // @ts-expect-error
                const position = document.caretPositionFromPoint(e.clientX, e.clientY);
                native = document.createRange();
                native.setStart(position.offsetNode, position.offset);
                native.setEnd(position.offsetNode, position.offset);
            }
            const normalized = native && quill.selection.normalizeNative(native);
            if (normalized) {
                const range = quill.selection.normalizedToRange(normalized);
                if (e.dataTransfer?.files) {
                    this.upload(range, e.dataTransfer.files);
                }
            }
        });
    }
    upload(range, files) {
        const uploads = [];
        Array.from(files).forEach((file)=>{
            if (file && this.options.mimetypes?.includes(file.type)) {
                uploads.push(file);
            }
        });
        if (uploads.length > 0) {
            // @ts-expect-error Fix me later
            this.options.handler.call(this, range, uploads);
        }
    }
}
Uploader.DEFAULTS = {
    mimetypes: [
        'image/png',
        'image/jpeg'
    ],
    handler (range, files) {
        if (!this.quill.scroll.query('image')) {
            return;
        }
        const promises = files.map((file)=>{
            return new Promise((resolve)=>{
                const reader = new FileReader();
                reader.onload = ()=>{
                    resolve(reader.result);
                };
                reader.readAsDataURL(file);
            });
        });
        Promise.all(promises).then((images)=>{
            const update = images.reduce((delta, image)=>{
                return delta.insert({
                    image
                });
            }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).delete(range.length));
            this.quill.updateContents(update, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
            this.quill.setSelection(range.index + images.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.SILENT);
        });
    }
};
const __TURBOPACK__default__export__ = Uploader;
 //# sourceMappingURL=uploader.js.map
}}),
"[project]/node_modules/quill/modules/input.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/keyboard.js [app-ssr] (ecmascript)");
;
;
;
;
const INSERT_TYPES = [
    'insertText',
    'insertReplacementText'
];
class Input extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        super(quill, options);
        quill.root.addEventListener('beforeinput', (event)=>{
            this.handleBeforeInput(event);
        });
        // Gboard with English input on Android triggers `compositionstart` sometimes even
        // users are not going to type anything.
        if (!/Android/i.test(navigator.userAgent)) {
            quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.COMPOSITION_BEFORE_START, ()=>{
                this.handleCompositionStart();
            });
        }
    }
    deleteRange(range) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteRange"])({
            range,
            quill: this.quill
        });
    }
    replaceText(range) {
        let text = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
        if (range.length === 0) return false;
        if (text) {
            // Follow the native behavior that inherits the formats of the first character
            const formats = this.quill.getFormat(range.index, 1);
            this.deleteRange(range);
            this.quill.updateContents(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index).insert(text, formats), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        } else {
            this.deleteRange(range);
        }
        this.quill.setSelection(range.index + text.length, 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        return true;
    }
    handleBeforeInput(event) {
        if (this.quill.composition.isComposing || event.defaultPrevented || !INSERT_TYPES.includes(event.inputType)) {
            return;
        }
        const staticRange = event.getTargetRanges ? event.getTargetRanges()[0] : null;
        if (!staticRange || staticRange.collapsed === true) {
            return;
        }
        const text = getPlainTextFromInputEvent(event);
        if (text == null) {
            return;
        }
        const normalized = this.quill.selection.normalizeNative(staticRange);
        const range = normalized ? this.quill.selection.normalizedToRange(normalized) : null;
        if (range && this.replaceText(range, text)) {
            event.preventDefault();
        }
    }
    handleCompositionStart() {
        const range = this.quill.getSelection();
        if (range) {
            this.replaceText(range);
        }
    }
}
function getPlainTextFromInputEvent(event) {
    // When `inputType` is "insertText":
    // - `event.data` should be string (Safari uses `event.dataTransfer`).
    // - `event.dataTransfer` should be null.
    // When `inputType` is "insertReplacementText":
    // - `event.data` should be null.
    // - `event.dataTransfer` should contain "text/plain" data.
    if (typeof event.data === 'string') {
        return event.data;
    }
    if (event.dataTransfer?.types.includes('text/plain')) {
        return event.dataTransfer.getData('text/plain');
    }
    return null;
}
const __TURBOPACK__default__export__ = Input;
 //# sourceMappingURL=input.js.map
}}),
"[project]/node_modules/quill/modules/uiNode.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TTL_FOR_VALID_SELECTION_CHANGE": (()=>TTL_FOR_VALID_SELECTION_CHANGE),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
const isMac = /Mac/i.test(navigator.platform);
const TTL_FOR_VALID_SELECTION_CHANGE = 100;
// A loose check to determine if the shortcut can move the caret before a UI node:
// <ANY_PARENT>[CARET]<div class="ql-ui"></div>[CONTENT]</ANY_PARENT>
const canMoveCaretBeforeUINode = (event)=>{
    if (event.key === 'ArrowLeft' || event.key === 'ArrowRight' || // RTL scripts or moving from the end of the previous line
    event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'Home') {
        return true;
    }
    if (isMac && event.key === 'a' && event.ctrlKey === true) {
        return true;
    }
    return false;
};
class UINode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    isListening = false;
    selectionChangeDeadline = 0;
    constructor(quill, options){
        super(quill, options);
        this.handleArrowKeys();
        this.handleNavigationShortcuts();
    }
    handleArrowKeys() {
        this.quill.keyboard.addBinding({
            key: [
                'ArrowLeft',
                'ArrowRight'
            ],
            offset: 0,
            shiftKey: null,
            handler (range, _ref) {
                let { line, event } = _ref;
                if (!(line instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParentBlot"]) || !line.uiNode) {
                    return true;
                }
                const isRTL = getComputedStyle(line.domNode)['direction'] === 'rtl';
                if (isRTL && event.key !== 'ArrowRight' || !isRTL && event.key !== 'ArrowLeft') {
                    return true;
                }
                this.quill.setSelection(range.index - 1, range.length + (event.shiftKey ? 1 : 0), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                return false;
            }
        });
    }
    handleNavigationShortcuts() {
        this.quill.root.addEventListener('keydown', (event)=>{
            if (!event.defaultPrevented && canMoveCaretBeforeUINode(event)) {
                this.ensureListeningToSelectionChange();
            }
        });
    }
    /**
   * We only listen to the `selectionchange` event when
   * there is an intention of moving the caret to the beginning using shortcuts.
   * This is primarily implemented to prevent infinite loops, as we are changing
   * the selection within the handler of a `selectionchange` event.
   */ ensureListeningToSelectionChange() {
        this.selectionChangeDeadline = Date.now() + TTL_FOR_VALID_SELECTION_CHANGE;
        if (this.isListening) return;
        this.isListening = true;
        const listener = ()=>{
            this.isListening = false;
            if (Date.now() <= this.selectionChangeDeadline) {
                this.handleSelectionChange();
            }
        };
        document.addEventListener('selectionchange', listener, {
            once: true
        });
    }
    handleSelectionChange() {
        const selection = document.getSelection();
        if (!selection) return;
        const range = selection.getRangeAt(0);
        if (range.collapsed !== true || range.startOffset !== 0) return;
        const line = this.quill.scroll.find(range.startContainer);
        if (!(line instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParentBlot"]) || !line.uiNode) return;
        const newRange = document.createRange();
        newRange.setStartAfter(line.uiNode);
        newRange.setEndAfter(line.uiNode);
        selection.removeAllRanges();
        selection.addRange(newRange);
    }
}
const __TURBOPACK__default__export__ = UINode;
 //# sourceMappingURL=uiNode.js.map
}}),
"[project]/node_modules/quill/core.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/embed.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/scroll.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$clipboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/clipboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$history$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/history.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/keyboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uploader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/uploader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$input$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/input.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uiNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/uiNode.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register({
    'blots/block': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/block/embed': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"],
    'blots/break': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/container': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/cursor': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/embed': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/inline': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/scroll': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'blots/text': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/clipboard': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$clipboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/history': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$history$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/keyboard': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/uploader': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uploader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/input': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$input$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/uiNode': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uiNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
 //# sourceMappingURL=core.js.map
}}),
"[project]/node_modules/quill/core.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/embed.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$scroll$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/scroll.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$clipboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/clipboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$history$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/history.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$keyboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/keyboard.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uploader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/uploader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$input$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/input.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$uiNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/uiNode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/quill/formats/indent.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
;
class IndentAttributor extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"] {
    add(node, value) {
        let normalizedValue = 0;
        if (value === '+1' || value === '-1') {
            const indent = this.value(node) || 0;
            normalizedValue = value === '+1' ? indent + 1 : indent - 1;
        } else if (typeof value === 'number') {
            normalizedValue = value;
        }
        if (normalizedValue === 0) {
            this.remove(node);
            return true;
        }
        return super.add(node, normalizedValue.toString());
    }
    canAdd(node, value) {
        return super.canAdd(node, value) || super.canAdd(node, parseInt(value, 10));
    }
    value(node) {
        return parseInt(super.value(node), 10) || undefined; // Don't return NaN
    }
}
const IndentClass = new IndentAttributor('indent', 'ql-indent', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].BLOCK,
    // @ts-expect-error
    whitelist: [
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8
    ]
});
const __TURBOPACK__default__export__ = IndentClass;
 //# sourceMappingURL=indent.js.map
}}),
"[project]/node_modules/quill/formats/blockquote.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
;
class Blockquote extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'blockquote';
    static tagName = 'blockquote';
}
const __TURBOPACK__default__export__ = Blockquote;
 //# sourceMappingURL=blockquote.js.map
}}),
"[project]/node_modules/quill/formats/header.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
;
class Header extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'header';
    static tagName = [
        'H1',
        'H2',
        'H3',
        'H4',
        'H5',
        'H6'
    ];
    static formats(domNode) {
        return this.tagName.indexOf(domNode.tagName) + 1;
    }
}
const __TURBOPACK__default__export__ = Header;
 //# sourceMappingURL=header.js.map
}}),
"[project]/node_modules/quill/formats/list.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ListContainer": (()=>ListContainer),
    "default": (()=>ListItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
class ListContainer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
}
ListContainer.blotName = 'list-container';
ListContainer.tagName = 'OL';
class ListItem extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static create(value) {
        const node = super.create();
        node.setAttribute('data-list', value);
        return node;
    }
    static formats(domNode) {
        return domNode.getAttribute('data-list') || undefined;
    }
    static register() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(ListContainer);
    }
    constructor(scroll, domNode){
        super(scroll, domNode);
        const ui = domNode.ownerDocument.createElement('span');
        const listEventHandler = (e)=>{
            if (!scroll.isEnabled()) return;
            const format = this.statics.formats(domNode, scroll);
            if (format === 'checked') {
                this.format('list', 'unchecked');
                e.preventDefault();
            } else if (format === 'unchecked') {
                this.format('list', 'checked');
                e.preventDefault();
            }
        };
        ui.addEventListener('mousedown', listEventHandler);
        ui.addEventListener('touchstart', listEventHandler);
        this.attachUI(ui);
    }
    format(name, value) {
        if (name === this.statics.blotName && value) {
            this.domNode.setAttribute('data-list', value);
        } else {
            super.format(name, value);
        }
    }
}
ListItem.blotName = 'list';
ListItem.tagName = 'LI';
ListContainer.allowedChildren = [
    ListItem
];
ListItem.requiredContainer = ListContainer;
;
 //# sourceMappingURL=list.js.map
}}),
"[project]/node_modules/quill/formats/bold.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
;
class Bold extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'bold';
    static tagName = [
        'STRONG',
        'B'
    ];
    static create() {
        return super.create();
    }
    static formats() {
        return true;
    }
    optimize(context) {
        super.optimize(context);
        if (this.domNode.tagName !== this.statics.tagName[0]) {
            this.replaceWith(this.statics.blotName);
        }
    }
}
const __TURBOPACK__default__export__ = Bold;
 //# sourceMappingURL=bold.js.map
}}),
"[project]/node_modules/quill/formats/italic.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/bold.js [app-ssr] (ecmascript)");
;
class Italic extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'italic';
    static tagName = [
        'EM',
        'I'
    ];
}
const __TURBOPACK__default__export__ = Italic;
 //# sourceMappingURL=italic.js.map
}}),
"[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Link),
    "sanitize": (()=>sanitize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
;
class Link extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'link';
    static tagName = 'A';
    static SANITIZED_URL = 'about:blank';
    static PROTOCOL_WHITELIST = [
        'http',
        'https',
        'mailto',
        'tel',
        'sms'
    ];
    static create(value) {
        const node = super.create(value);
        node.setAttribute('href', this.sanitize(value));
        node.setAttribute('rel', 'noopener noreferrer');
        node.setAttribute('target', '_blank');
        return node;
    }
    static formats(domNode) {
        return domNode.getAttribute('href');
    }
    static sanitize(url) {
        return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;
    }
    format(name, value) {
        if (name !== this.statics.blotName || !value) {
            super.format(name, value);
        } else {
            // @ts-expect-error
            this.domNode.setAttribute('href', this.constructor.sanitize(value));
        }
    }
}
function sanitize(url, protocols) {
    const anchor = document.createElement('a');
    anchor.href = url;
    const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));
    return protocols.indexOf(protocol) > -1;
}
;
 //# sourceMappingURL=link.js.map
}}),
"[project]/node_modules/quill/formats/script.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
;
class Script extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'script';
    static tagName = [
        'SUB',
        'SUP'
    ];
    static create(value) {
        if (value === 'super') {
            return document.createElement('sup');
        }
        if (value === 'sub') {
            return document.createElement('sub');
        }
        return super.create(value);
    }
    static formats(domNode) {
        if (domNode.tagName === 'SUB') return 'sub';
        if (domNode.tagName === 'SUP') return 'super';
        return undefined;
    }
}
const __TURBOPACK__default__export__ = Script;
 //# sourceMappingURL=script.js.map
}}),
"[project]/node_modules/quill/formats/strike.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/bold.js [app-ssr] (ecmascript)");
;
class Strike extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'strike';
    static tagName = [
        'S',
        'STRIKE'
    ];
}
const __TURBOPACK__default__export__ = Strike;
 //# sourceMappingURL=strike.js.map
}}),
"[project]/node_modules/quill/formats/underline.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
;
class Underline extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'underline';
    static tagName = 'U';
}
const __TURBOPACK__default__export__ = Underline;
 //# sourceMappingURL=underline.js.map
}}),
"[project]/node_modules/quill/formats/formula.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/embed.js [app-ssr] (ecmascript)");
;
class Formula extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$embed$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'formula';
    static className = 'ql-formula';
    static tagName = 'SPAN';
    static create(value) {
        // @ts-expect-error
        if (window.katex == null) {
            throw new Error('Formula module requires KaTeX.');
        }
        const node = super.create(value);
        if (typeof value === 'string') {
            // @ts-expect-error
            window.katex.render(value, node, {
                throwOnError: false,
                errorColor: '#f00'
            });
            node.setAttribute('data-value', value);
        }
        return node;
    }
    static value(domNode) {
        return domNode.getAttribute('data-value');
    }
    html() {
        const { formula } = this.value();
        return `<span>${formula}</span>`;
    }
}
const __TURBOPACK__default__export__ = Formula;
 //# sourceMappingURL=formula.js.map
}}),
"[project]/node_modules/quill/formats/image.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)");
;
;
const ATTRIBUTES = [
    'alt',
    'height',
    'width'
];
class Image extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"] {
    static blotName = 'image';
    static tagName = 'IMG';
    static create(value) {
        const node = super.create(value);
        if (typeof value === 'string') {
            node.setAttribute('src', this.sanitize(value));
        }
        return node;
    }
    static formats(domNode) {
        return ATTRIBUTES.reduce((formats, attribute)=>{
            if (domNode.hasAttribute(attribute)) {
                formats[attribute] = domNode.getAttribute(attribute);
            }
            return formats;
        }, {});
    }
    static match(url) {
        return /\.(jpe?g|gif|png)$/.test(url) || /^data:image\/.+;base64/.test(url);
    }
    static sanitize(url) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sanitize"])(url, [
            'http',
            'https',
            'data'
        ]) ? url : '//:0';
    }
    static value(domNode) {
        return domNode.getAttribute('src');
    }
    format(name, value) {
        if (ATTRIBUTES.indexOf(name) > -1) {
            if (value) {
                this.domNode.setAttribute(name, value);
            } else {
                this.domNode.removeAttribute(name);
            }
        } else {
            super.format(name, value);
        }
    }
}
const __TURBOPACK__default__export__ = Image;
 //# sourceMappingURL=image.js.map
}}),
"[project]/node_modules/quill/formats/video.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)");
;
;
const ATTRIBUTES = [
    'height',
    'width'
];
class Video extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BlockEmbed"] {
    static blotName = 'video';
    static className = 'ql-video';
    static tagName = 'IFRAME';
    static create(value) {
        const node = super.create(value);
        node.setAttribute('frameborder', '0');
        node.setAttribute('allowfullscreen', 'true');
        node.setAttribute('src', this.sanitize(value));
        return node;
    }
    static formats(domNode) {
        return ATTRIBUTES.reduce((formats, attribute)=>{
            if (domNode.hasAttribute(attribute)) {
                formats[attribute] = domNode.getAttribute(attribute);
            }
            return formats;
        }, {});
    }
    static sanitize(url) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sanitize(url);
    }
    static value(domNode) {
        return domNode.getAttribute('src');
    }
    format(name, value) {
        if (ATTRIBUTES.indexOf(name) > -1) {
            if (value) {
                this.domNode.setAttribute(name, value);
            } else {
                this.domNode.removeAttribute(name);
            }
        } else {
            super.format(name, value);
        }
    }
    html() {
        const { video } = this.value();
        return `<a href="${video}">${video}</a>`;
    }
}
const __TURBOPACK__default__export__ = Video;
 //# sourceMappingURL=video.js.map
}}),
"[project]/node_modules/quill/modules/syntax.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CodeBlock": (()=>SyntaxCodeBlock),
    "CodeToken": (()=>CodeToken),
    "default": (()=>Syntax)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/inline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/break.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/cursor.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/text.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$clipboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/clipboard.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const TokenAttributor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ClassAttributor"]('code-token', 'hljs', {
    scope: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE
});
class CodeToken extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$inline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static formats(node, scroll) {
        while(node != null && node !== scroll.domNode){
            if (node.classList && node.classList.contains(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].className)) {
                // @ts-expect-error
                return super.formats(node, scroll);
            }
            // @ts-expect-error
            node = node.parentNode;
        }
        return undefined;
    }
    constructor(scroll, domNode, value){
        // @ts-expect-error
        super(scroll, domNode, value);
        TokenAttributor.add(this.domNode, value);
    }
    format(format, value) {
        if (format !== CodeToken.blotName) {
            super.format(format, value);
        } else if (value) {
            TokenAttributor.add(this.domNode, value);
        } else {
            TokenAttributor.remove(this.domNode);
            this.domNode.classList.remove(this.statics.className);
        }
    }
    optimize() {
        // @ts-expect-error
        super.optimize(...arguments);
        if (!TokenAttributor.value(this.domNode)) {
            this.unwrap();
        }
    }
}
CodeToken.blotName = 'code-token';
CodeToken.className = 'ql-token';
class SyntaxCodeBlock extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static create(value) {
        const domNode = super.create(value);
        if (typeof value === 'string') {
            domNode.setAttribute('data-language', value);
        }
        return domNode;
    }
    static formats(domNode) {
        // @ts-expect-error
        return domNode.getAttribute('data-language') || 'plain';
    }
    static register() {}
    format(name, value) {
        if (name === this.statics.blotName && value) {
            // @ts-expect-error
            this.domNode.setAttribute('data-language', value);
        } else {
            super.format(name, value);
        }
    }
    replaceWith(name, value) {
        this.formatAt(0, this.length(), CodeToken.blotName, false);
        return super.replaceWith(name, value);
    }
}
class SyntaxCodeBlockContainer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CodeBlockContainer"] {
    attach() {
        super.attach();
        this.forceNext = false;
        // @ts-expect-error
        this.scroll.emitMount(this);
    }
    format(name, value) {
        if (name === SyntaxCodeBlock.blotName) {
            this.forceNext = true;
            this.children.forEach((child)=>{
                // @ts-expect-error
                child.format(name, value);
            });
        }
    }
    formatAt(index, length, name, value) {
        if (name === SyntaxCodeBlock.blotName) {
            this.forceNext = true;
        }
        super.formatAt(index, length, name, value);
    }
    highlight(highlight) {
        let forced = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        if (this.children.head == null) return;
        const nodes = Array.from(this.domNode.childNodes).filter((node)=>node !== this.uiNode);
        const text = `${nodes.map((node)=>node.textContent).join('\n')}\n`;
        const language = SyntaxCodeBlock.formats(this.children.head.domNode);
        if (forced || this.forceNext || this.cachedText !== text) {
            if (text.trim().length > 0 || this.cachedText == null) {
                const oldDelta = this.children.reduce((delta, child)=>{
                    // @ts-expect-error
                    return delta.concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["blockDelta"])(child, false));
                }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
                const delta = highlight(text, language);
                oldDelta.diff(delta).reduce((index, _ref)=>{
                    let { retain, attributes } = _ref;
                    // Should be all retains
                    if (!retain) return index;
                    if (attributes) {
                        Object.keys(attributes).forEach((format)=>{
                            if ([
                                SyntaxCodeBlock.blotName,
                                CodeToken.blotName
                            ].includes(format)) {
                                // @ts-expect-error
                                this.formatAt(index, retain, format, attributes[format]);
                            }
                        });
                    }
                    // @ts-expect-error
                    return index + retain;
                }, 0);
            }
            this.cachedText = text;
            this.forceNext = false;
        }
    }
    html(index, length) {
        const [codeBlock] = this.children.find(index);
        const language = codeBlock ? SyntaxCodeBlock.formats(codeBlock.domNode) : 'plain';
        return `<pre data-language="${language}">\n${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["escapeText"])(this.code(index, length))}\n</pre>`;
    }
    optimize(context) {
        super.optimize(context);
        if (this.parent != null && this.children.head != null && this.uiNode != null) {
            const language = SyntaxCodeBlock.formats(this.children.head.domNode);
            // @ts-expect-error
            if (language !== this.uiNode.value) {
                // @ts-expect-error
                this.uiNode.value = language;
            }
        }
    }
}
SyntaxCodeBlockContainer.allowedChildren = [
    SyntaxCodeBlock
];
SyntaxCodeBlock.requiredContainer = SyntaxCodeBlockContainer;
SyntaxCodeBlock.allowedChildren = [
    CodeToken,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$cursor$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$break$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
];
const highlight = (lib, language, text)=>{
    if (typeof lib.versionString === 'string') {
        const majorVersion = lib.versionString.split('.')[0];
        if (parseInt(majorVersion, 10) >= 11) {
            return lib.highlight(text, {
                language
            }).value;
        }
    }
    return lib.highlight(language, text).value;
};
class Syntax extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static register() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(CodeToken, true);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(SyntaxCodeBlock, true);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(SyntaxCodeBlockContainer, true);
    }
    constructor(quill, options){
        super(quill, options);
        if (this.options.hljs == null) {
            throw new Error('Syntax module requires highlight.js. Please include the library on the page before Quill.');
        }
        // @ts-expect-error Fix me later
        this.languages = this.options.languages.reduce((memo, _ref2)=>{
            let { key } = _ref2;
            memo[key] = true;
            return memo;
        }, {});
        this.highlightBlot = this.highlightBlot.bind(this);
        this.initListener();
        this.initTimer();
    }
    initListener() {
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.SCROLL_BLOT_MOUNT, (blot)=>{
            if (!(blot instanceof SyntaxCodeBlockContainer)) return;
            const select = this.quill.root.ownerDocument.createElement('select');
            // @ts-expect-error Fix me later
            this.options.languages.forEach((_ref3)=>{
                let { key, label } = _ref3;
                const option = select.ownerDocument.createElement('option');
                option.textContent = label;
                option.setAttribute('value', key);
                select.appendChild(option);
            });
            select.addEventListener('change', ()=>{
                blot.format(SyntaxCodeBlock.blotName, select.value);
                this.quill.root.focus(); // Prevent scrolling
                this.highlight(blot, true);
            });
            if (blot.uiNode == null) {
                blot.attachUI(select);
                if (blot.children.head) {
                    select.value = SyntaxCodeBlock.formats(blot.children.head.domNode);
                }
            }
        });
    }
    initTimer() {
        let timer = null;
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.SCROLL_OPTIMIZE, ()=>{
            if (timer) {
                clearTimeout(timer);
            }
            timer = setTimeout(()=>{
                this.highlight();
                timer = null;
            }, this.options.interval);
        });
    }
    highlight() {
        let blot = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
        let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        if (this.quill.selection.composing) return;
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        const range = this.quill.getSelection();
        const blots = blot == null ? this.quill.scroll.descendants(SyntaxCodeBlockContainer) : [
            blot
        ];
        blots.forEach((container)=>{
            container.highlight(this.highlightBlot, force);
        });
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        if (range != null) {
            this.quill.setSelection(range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        }
    }
    highlightBlot(text) {
        let language = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'plain';
        language = this.languages[language] ? language : 'plain';
        if (language === 'plain') {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["escapeText"])(text).split('\n').reduce((delta, line, i)=>{
                if (i !== 0) {
                    delta.insert('\n', {
                        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName]: language
                    });
                }
                return delta.insert(line);
            }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]());
        }
        const container = this.quill.root.ownerDocument.createElement('div');
        container.classList.add(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].className);
        container.innerHTML = highlight(this.options.hljs, language, text);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$clipboard$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["traverse"])(this.quill.scroll, container, [
            (node, delta)=>{
                // @ts-expect-error
                const value = TokenAttributor.value(node);
                if (value) {
                    return delta.compose(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(delta.length(), {
                        [CodeToken.blotName]: value
                    }));
                }
                return delta;
            }
        ], [
            (node, delta)=>{
                // @ts-expect-error
                return node.data.split('\n').reduce((memo, nodeText, i)=>{
                    if (i !== 0) memo.insert('\n', {
                        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].blotName]: language
                    });
                    return memo.insert(nodeText);
                }, delta);
            }
        ], new WeakMap());
    }
}
Syntax.DEFAULTS = {
    hljs: (()=>{
        return window.hljs;
    })(),
    interval: 1000,
    languages: [
        {
            key: 'plain',
            label: 'Plain'
        },
        {
            key: 'bash',
            label: 'Bash'
        },
        {
            key: 'cpp',
            label: 'C++'
        },
        {
            key: 'cs',
            label: 'C#'
        },
        {
            key: 'css',
            label: 'CSS'
        },
        {
            key: 'diff',
            label: 'Diff'
        },
        {
            key: 'xml',
            label: 'HTML/XML'
        },
        {
            key: 'java',
            label: 'Java'
        },
        {
            key: 'javascript',
            label: 'JavaScript'
        },
        {
            key: 'markdown',
            label: 'Markdown'
        },
        {
            key: 'php',
            label: 'PHP'
        },
        {
            key: 'python',
            label: 'Python'
        },
        {
            key: 'ruby',
            label: 'Ruby'
        },
        {
            key: 'sql',
            label: 'SQL'
        }
    ]
};
;
 //# sourceMappingURL=syntax.js.map
}}),
"[project]/node_modules/quill/formats/table.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableBody": (()=>TableBody),
    "TableCell": (()=>TableCell),
    "TableContainer": (()=>TableContainer),
    "TableRow": (()=>TableRow),
    "tableId": (()=>tableId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/block.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/blots/container.js [app-ssr] (ecmascript)");
;
;
class TableCell extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$block$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'table';
    static tagName = 'TD';
    static create(value) {
        const node = super.create();
        if (value) {
            node.setAttribute('data-row', value);
        } else {
            node.setAttribute('data-row', tableId());
        }
        return node;
    }
    static formats(domNode) {
        if (domNode.hasAttribute('data-row')) {
            return domNode.getAttribute('data-row');
        }
        return undefined;
    }
    cellOffset() {
        if (this.parent) {
            return this.parent.children.indexOf(this);
        }
        return -1;
    }
    format(name, value) {
        if (name === TableCell.blotName && value) {
            this.domNode.setAttribute('data-row', value);
        } else {
            super.format(name, value);
        }
    }
    row() {
        return this.parent;
    }
    rowOffset() {
        if (this.row()) {
            return this.row().rowOffset();
        }
        return -1;
    }
    table() {
        return this.row() && this.row().table();
    }
}
class TableRow extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'table-row';
    static tagName = 'TR';
    checkMerge() {
        // @ts-expect-error
        if (super.checkMerge() && this.next.children.head != null) {
            // @ts-expect-error
            const thisHead = this.children.head.formats();
            // @ts-expect-error
            const thisTail = this.children.tail.formats();
            // @ts-expect-error
            const nextHead = this.next.children.head.formats();
            // @ts-expect-error
            const nextTail = this.next.children.tail.formats();
            return thisHead.table === thisTail.table && thisHead.table === nextHead.table && thisHead.table === nextTail.table;
        }
        return false;
    }
    optimize(context) {
        super.optimize(context);
        this.children.forEach((child)=>{
            if (child.next == null) return;
            const childFormats = child.formats();
            const nextFormats = child.next.formats();
            if (childFormats.table !== nextFormats.table) {
                const next = this.splitAfter(child);
                if (next) {
                    // @ts-expect-error TODO: parameters of optimize() should be a optional
                    next.optimize();
                }
                // We might be able to merge with prev now
                if (this.prev) {
                    // @ts-expect-error TODO: parameters of optimize() should be a optional
                    this.prev.optimize();
                }
            }
        });
    }
    rowOffset() {
        if (this.parent) {
            return this.parent.children.indexOf(this);
        }
        return -1;
    }
    table() {
        return this.parent && this.parent.parent;
    }
}
class TableBody extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'table-body';
    static tagName = 'TBODY';
}
class TableContainer extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$blots$2f$container$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static blotName = 'table-container';
    static tagName = 'TABLE';
    balanceCells() {
        const rows = this.descendants(TableRow);
        const maxColumns = rows.reduce((max, row)=>{
            return Math.max(row.children.length, max);
        }, 0);
        rows.forEach((row)=>{
            new Array(maxColumns - row.children.length).fill(0).forEach(()=>{
                let value;
                if (row.children.head != null) {
                    value = TableCell.formats(row.children.head.domNode);
                }
                const blot = this.scroll.create(TableCell.blotName, value);
                row.appendChild(blot);
                // @ts-expect-error TODO: parameters of optimize() should be a optional
                blot.optimize(); // Add break blot
            });
        });
    }
    cells(column) {
        return this.rows().map((row)=>row.children.at(column));
    }
    deleteColumn(index) {
        // @ts-expect-error
        const [body] = this.descendant(TableBody);
        if (body == null || body.children.head == null) return;
        body.children.forEach((row)=>{
            const cell = row.children.at(index);
            if (cell != null) {
                cell.remove();
            }
        });
    }
    insertColumn(index) {
        // @ts-expect-error
        const [body] = this.descendant(TableBody);
        if (body == null || body.children.head == null) return;
        body.children.forEach((row)=>{
            const ref = row.children.at(index);
            // @ts-expect-error
            const value = TableCell.formats(row.children.head.domNode);
            const cell = this.scroll.create(TableCell.blotName, value);
            row.insertBefore(cell, ref);
        });
    }
    insertRow(index) {
        // @ts-expect-error
        const [body] = this.descendant(TableBody);
        if (body == null || body.children.head == null) return;
        const id = tableId();
        const row = this.scroll.create(TableRow.blotName);
        body.children.head.children.forEach(()=>{
            const cell = this.scroll.create(TableCell.blotName, id);
            row.appendChild(cell);
        });
        const ref = body.children.at(index);
        body.insertBefore(row, ref);
    }
    rows() {
        const body = this.children.head;
        if (body == null) return [];
        return body.children.map((row)=>row);
    }
}
TableContainer.allowedChildren = [
    TableBody
];
TableBody.requiredContainer = TableContainer;
TableBody.allowedChildren = [
    TableRow
];
TableRow.requiredContainer = TableBody;
TableRow.allowedChildren = [
    TableCell
];
TableCell.requiredContainer = TableRow;
function tableId() {
    const id = Math.random().toString(36).slice(2, 6);
    return `row-${id}`;
}
;
 //# sourceMappingURL=table.js.map
}}),
"[project]/node_modules/quill/modules/table.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/table.js [app-ssr] (ecmascript)");
;
;
;
;
class Table extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    static register() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"]);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableRow"]);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableBody"]);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableContainer"]);
    }
    constructor(){
        super(...arguments);
        this.listenBalanceCells();
    }
    balanceTables() {
        this.quill.scroll.descendants(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableContainer"]).forEach((table)=>{
            table.balanceCells();
        });
    }
    deleteColumn() {
        const [table, , cell] = this.getTable();
        if (cell == null) return;
        // @ts-expect-error
        table.deleteColumn(cell.cellOffset());
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
    }
    deleteRow() {
        const [, row] = this.getTable();
        if (row == null) return;
        row.remove();
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
    }
    deleteTable() {
        const [table] = this.getTable();
        if (table == null) return;
        // @ts-expect-error
        const offset = table.offset();
        // @ts-expect-error
        table.remove();
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.quill.setSelection(offset, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
    }
    getTable() {
        let range = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.quill.getSelection();
        if (range == null) return [
            null,
            null,
            null,
            -1
        ];
        const [cell, offset] = this.quill.getLine(range.index);
        if (cell == null || cell.statics.blotName !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"].blotName) {
            return [
                null,
                null,
                null,
                -1
            ];
        }
        const row = cell.parent;
        const table = row.parent.parent;
        // @ts-expect-error
        return [
            table,
            row,
            cell,
            offset
        ];
    }
    insertColumn(offset) {
        const range = this.quill.getSelection();
        if (!range) return;
        const [table, row, cell] = this.getTable(range);
        if (cell == null) return;
        const column = cell.cellOffset();
        table.insertColumn(column + offset);
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        let shift = row.rowOffset();
        if (offset === 0) {
            shift += 1;
        }
        this.quill.setSelection(range.index + shift, range.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
    }
    insertColumnLeft() {
        this.insertColumn(0);
    }
    insertColumnRight() {
        this.insertColumn(1);
    }
    insertRow(offset) {
        const range = this.quill.getSelection();
        if (!range) return;
        const [table, row, cell] = this.getTable(range);
        if (cell == null) return;
        const index = row.rowOffset();
        table.insertRow(index + offset);
        this.quill.update(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        if (offset > 0) {
            this.quill.setSelection(range, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        } else {
            this.quill.setSelection(range.index + row.children.length, range.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        }
    }
    insertRowAbove() {
        this.insertRow(0);
    }
    insertRowBelow() {
        this.insertRow(1);
    }
    insertTable(rows, columns) {
        const range = this.quill.getSelection();
        if (range == null) return;
        const delta = new Array(rows).fill(0).reduce((memo)=>{
            const text = new Array(columns).fill('\n').join('');
            return memo.insert(text, {
                table: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["tableId"])()
            });
        }, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]().retain(range.index));
        this.quill.updateContents(delta, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        this.quill.setSelection(range.index, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.SILENT);
        this.balanceTables();
    }
    listenBalanceCells() {
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.SCROLL_OPTIMIZE, (mutations)=>{
            mutations.some((mutation)=>{
                if ([
                    'TD',
                    'TR',
                    'TBODY',
                    'TABLE'
                ].includes(mutation.target.tagName)) {
                    this.quill.once(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.TEXT_CHANGE, (delta, old, source)=>{
                        if (source !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER) return;
                        this.balanceTables();
                    });
                    return true;
                }
                return false;
            });
        });
    }
}
const __TURBOPACK__default__export__ = Table;
 //# sourceMappingURL=table.js.map
}}),
"[project]/node_modules/quill/modules/toolbar.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addControls": (()=>addControls),
    "default": (()=>Toolbar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill-delta/dist/Delta.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parchment/dist/parchment.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/logger.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/module.js [app-ssr] (ecmascript)");
;
;
;
;
;
const debug = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$logger$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])('quill:toolbar');
class Toolbar extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        super(quill, options);
        if (Array.isArray(this.options.container)) {
            const container = document.createElement('div');
            container.setAttribute('role', 'toolbar');
            addControls(container, this.options.container);
            quill.container?.parentNode?.insertBefore(container, quill.container);
            this.container = container;
        } else if (typeof this.options.container === 'string') {
            this.container = document.querySelector(this.options.container);
        } else {
            this.container = this.options.container;
        }
        if (!(this.container instanceof HTMLElement)) {
            debug.error('Container required for toolbar', this.options);
            return;
        }
        this.container.classList.add('ql-toolbar');
        this.controls = [];
        this.handlers = {};
        if (this.options.handlers) {
            Object.keys(this.options.handlers).forEach((format)=>{
                const handler = this.options.handlers?.[format];
                if (handler) {
                    this.addHandler(format, handler);
                }
            });
        }
        Array.from(this.container.querySelectorAll('button, select')).forEach((input)=>{
            // @ts-expect-error
            this.attach(input);
        });
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].events.EDITOR_CHANGE, ()=>{
            const [range] = this.quill.selection.getRange(); // quill.getSelection triggers update
            this.update(range);
        });
    }
    addHandler(format, handler) {
        this.handlers[format] = handler;
    }
    attach(input) {
        let format = Array.from(input.classList).find((className)=>{
            return className.indexOf('ql-') === 0;
        });
        if (!format) return;
        format = format.slice('ql-'.length);
        if (input.tagName === 'BUTTON') {
            input.setAttribute('type', 'button');
        }
        if (this.handlers[format] == null && this.quill.scroll.query(format) == null) {
            debug.warn('ignoring attaching to nonexistent format', format, input);
            return;
        }
        const eventName = input.tagName === 'SELECT' ? 'change' : 'click';
        input.addEventListener(eventName, (e)=>{
            let value;
            if (input.tagName === 'SELECT') {
                // @ts-expect-error
                if (input.selectedIndex < 0) return;
                // @ts-expect-error
                const selected = input.options[input.selectedIndex];
                if (selected.hasAttribute('selected')) {
                    value = false;
                } else {
                    value = selected.value || false;
                }
            } else {
                if (input.classList.contains('ql-active')) {
                    value = false;
                } else {
                    // @ts-expect-error
                    value = input.value || !input.hasAttribute('value');
                }
                e.preventDefault();
            }
            this.quill.focus();
            const [range] = this.quill.selection.getRange();
            if (this.handlers[format] != null) {
                this.handlers[format].call(this, value);
            } else if (// @ts-expect-error
            this.quill.scroll.query(format).prototype instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmbedBlot"]) {
                value = prompt(`Enter ${format}`); // eslint-disable-line no-alert
                if (!value) return;
                this.quill.updateContents(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2d$delta$2f$dist$2f$Delta$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]()// @ts-expect-error Fix me later
                .retain(range.index)// @ts-expect-error Fix me later
                .delete(range.length).insert({
                    [format]: value
                }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            } else {
                this.quill.format(format, value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
            this.update(range);
        });
        this.controls.push([
            format,
            input
        ]);
    }
    update(range) {
        const formats = range == null ? {} : this.quill.getFormat(range);
        this.controls.forEach((pair)=>{
            const [format, input] = pair;
            if (input.tagName === 'SELECT') {
                let option = null;
                if (range == null) {
                    option = null;
                } else if (formats[format] == null) {
                    option = input.querySelector('option[selected]');
                } else if (!Array.isArray(formats[format])) {
                    let value = formats[format];
                    if (typeof value === 'string') {
                        value = value.replace(/"/g, '\\"');
                    }
                    option = input.querySelector(`option[value="${value}"]`);
                }
                if (option == null) {
                    // @ts-expect-error TODO fix me later
                    input.value = ''; // TODO make configurable?
                    // @ts-expect-error TODO fix me later
                    input.selectedIndex = -1;
                } else {
                    option.selected = true;
                }
            } else if (range == null) {
                input.classList.remove('ql-active');
                input.setAttribute('aria-pressed', 'false');
            } else if (input.hasAttribute('value')) {
                // both being null should match (default values)
                // '1' should match with 1 (headers)
                const value = formats[format];
                const isActive = value === input.getAttribute('value') || value != null && value.toString() === input.getAttribute('value') || value == null && !input.getAttribute('value');
                input.classList.toggle('ql-active', isActive);
                input.setAttribute('aria-pressed', isActive.toString());
            } else {
                const isActive = formats[format] != null;
                input.classList.toggle('ql-active', isActive);
                input.setAttribute('aria-pressed', isActive.toString());
            }
        });
    }
}
Toolbar.DEFAULTS = {};
function addButton(container, format, value) {
    const input = document.createElement('button');
    input.setAttribute('type', 'button');
    input.classList.add(`ql-${format}`);
    input.setAttribute('aria-pressed', 'false');
    if (value != null) {
        input.value = value;
        input.setAttribute('aria-label', `${format}: ${value}`);
    } else {
        input.setAttribute('aria-label', format);
    }
    container.appendChild(input);
}
function addControls(container, groups) {
    if (!Array.isArray(groups[0])) {
        // @ts-expect-error
        groups = [
            groups
        ];
    }
    groups.forEach((controls)=>{
        const group = document.createElement('span');
        group.classList.add('ql-formats');
        controls.forEach((control)=>{
            if (typeof control === 'string') {
                addButton(group, control);
            } else {
                const format = Object.keys(control)[0];
                const value = control[format];
                if (Array.isArray(value)) {
                    addSelect(group, format, value);
                } else {
                    addButton(group, format, value);
                }
            }
        });
        container.appendChild(group);
    });
}
function addSelect(container, format, values) {
    const input = document.createElement('select');
    input.classList.add(`ql-${format}`);
    values.forEach((value)=>{
        const option = document.createElement('option');
        if (value !== false) {
            option.setAttribute('value', String(value));
        } else {
            option.setAttribute('selected', 'selected');
        }
        input.appendChild(option);
    });
    container.appendChild(input);
}
Toolbar.DEFAULTS = {
    container: null,
    handlers: {
        clean () {
            const range = this.quill.getSelection();
            if (range == null) return;
            if (range.length === 0) {
                const formats = this.quill.getFormat();
                Object.keys(formats).forEach((name)=>{
                    // Clean functionality in existing apps only clean inline formats
                    if (this.quill.scroll.query(name, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parchment$2f$dist$2f$parchment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scope"].INLINE) != null) {
                        this.quill.format(name, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    }
                });
            } else {
                this.quill.removeFormat(range.index, range.length, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        },
        direction (value) {
            const { align } = this.quill.getFormat();
            if (value === 'rtl' && align == null) {
                this.quill.format('align', 'right', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            } else if (!value && align === 'right') {
                this.quill.format('align', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
            this.quill.format('direction', value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        },
        indent (value) {
            const range = this.quill.getSelection();
            // @ts-expect-error
            const formats = this.quill.getFormat(range);
            // @ts-expect-error
            const indent = parseInt(formats.indent || 0, 10);
            if (value === '+1' || value === '-1') {
                let modifier = value === '+1' ? 1 : -1;
                if (formats.direction === 'rtl') modifier *= -1;
                this.quill.format('indent', indent + modifier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        },
        link (value) {
            if (value === true) {
                value = prompt('Enter link URL:'); // eslint-disable-line no-alert
            }
            this.quill.format('link', value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
        },
        list (value) {
            const range = this.quill.getSelection();
            // @ts-expect-error
            const formats = this.quill.getFormat(range);
            if (value === 'check') {
                if (formats.list === 'checked' || formats.list === 'unchecked') {
                    this.quill.format('list', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                } else {
                    this.quill.format('list', 'unchecked', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                }
            } else {
                this.quill.format('list', value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
            }
        }
    }
};
;
 //# sourceMappingURL=toolbar.js.map
}}),
"[project]/node_modules/quill/ui/icons.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const alignLeftIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"13\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"9\" y1=\"4\" y2=\"4\"/></svg>";
const alignCenterIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"14\" x2=\"4\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"12\" x2=\"6\" y1=\"4\" y2=\"4\"/></svg>";
const alignRightIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"5\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"9\" y1=\"4\" y2=\"4\"/></svg>";
const alignJustifyIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"3\" y1=\"4\" y2=\"4\"/></svg>";
const backgroundIcon = "<svg viewbox=\"0 0 18 18\"><g class=\"ql-fill ql-color-label\"><polygon points=\"6 6.868 6 6 5 6 5 7 5.942 7 6 6.868\"/><rect height=\"1\" width=\"1\" x=\"4\" y=\"4\"/><polygon points=\"6.817 5 6 5 6 6 6.38 6 6.817 5\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"6\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"4\" y=\"7\"/><polygon points=\"4 11.439 4 11 3 11 3 12 3.755 12 4 11.439\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"12\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"9\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"15\"/><polygon points=\"4.63 10 4 10 4 11 4.192 11 4.63 10\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"8\"/><path d=\"M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z\"/><path d=\"M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z\"/><path d=\"M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z\"/><rect height=\"1\" width=\"1\" x=\"12\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"11\" y=\"3\"/><path d=\"M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z\"/><rect height=\"1\" width=\"1\" x=\"2\" y=\"3\"/><rect height=\"1\" width=\"1\" x=\"6\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"3\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"5\" y=\"3\"/><rect height=\"1\" width=\"1\" x=\"9\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"14\"/><polygon points=\"13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174\"/><rect height=\"1\" width=\"1\" x=\"13\" y=\"7\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"6\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"8\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"9\"/><path d=\"M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"3\"/><polygon points=\"12 6.868 12 6 11.62 6 12 6.868\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"2\"/><rect height=\"1\" width=\"1\" x=\"12\" y=\"5\"/><rect height=\"1\" width=\"1\" x=\"13\" y=\"4\"/><polygon points=\"12.933 9 13 9 13 8 12.495 8 12.933 9\"/><rect height=\"1\" width=\"1\" x=\"9\" y=\"14\"/><rect height=\"1\" width=\"1\" x=\"8\" y=\"15\"/><path d=\"M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z\"/><rect height=\"1\" width=\"1\" x=\"5\" y=\"15\"/><path d=\"M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z\"/><rect height=\"1\" width=\"1\" x=\"11\" y=\"15\"/><path d=\"M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z\"/><rect height=\"1\" width=\"1\" x=\"14\" y=\"15\"/><rect height=\"1\" width=\"1\" x=\"15\" y=\"11\"/></g><polyline class=\"ql-stroke\" points=\"5.5 13 9 5 12.5 13\"/><line class=\"ql-stroke\" x1=\"11.63\" x2=\"6.38\" y1=\"11\" y2=\"11\"/></svg>";
const blockquoteIcon = "<svg viewbox=\"0 0 18 18\"><rect class=\"ql-fill ql-stroke\" height=\"3\" width=\"3\" x=\"4\" y=\"5\"/><rect class=\"ql-fill ql-stroke\" height=\"3\" width=\"3\" x=\"11\" y=\"5\"/><path class=\"ql-even ql-fill ql-stroke\" d=\"M7,8c0,4.031-3,5-3,5\"/><path class=\"ql-even ql-fill ql-stroke\" d=\"M14,8c0,4.031-3,5-3,5\"/></svg>";
const boldIcon = "<svg viewbox=\"0 0 18 18\"><path class=\"ql-stroke\" d=\"M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z\"/><path class=\"ql-stroke\" d=\"M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z\"/></svg>";
const cleanIcon = "<svg class=\"\" viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"5\" x2=\"13\" y1=\"3\" y2=\"3\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"9.35\" y1=\"12\" y2=\"3\"/><line class=\"ql-stroke\" x1=\"11\" x2=\"15\" y1=\"11\" y2=\"15\"/><line class=\"ql-stroke\" x1=\"15\" x2=\"11\" y1=\"11\" y2=\"15\"/><rect class=\"ql-fill\" height=\"1\" rx=\"0.5\" ry=\"0.5\" width=\"7\" x=\"2\" y=\"14\"/></svg>";
const codeIcon = "<svg viewbox=\"0 0 18 18\"><polyline class=\"ql-even ql-stroke\" points=\"5 7 3 9 5 11\"/><polyline class=\"ql-even ql-stroke\" points=\"13 7 15 9 13 11\"/><line class=\"ql-stroke\" x1=\"10\" x2=\"8\" y1=\"5\" y2=\"13\"/></svg>";
const colorIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-color-label ql-stroke ql-transparent\" x1=\"3\" x2=\"15\" y1=\"15\" y2=\"15\"/><polyline class=\"ql-stroke\" points=\"5.5 11 9 3 12.5 11\"/><line class=\"ql-stroke\" x1=\"11.63\" x2=\"6.38\" y1=\"9\" y2=\"9\"/></svg>";
const directionLeftToRightIcon = "<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke ql-fill\" points=\"3 11 5 9 3 7 3 11\"/><line class=\"ql-stroke ql-fill\" x1=\"15\" x2=\"11\" y1=\"4\" y2=\"4\"/><path class=\"ql-fill\" d=\"M11,3a3,3,0,0,0,0,6h1V3H11Z\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"11\" y=\"4\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"13\" y=\"4\"/></svg>";
const directionRightToLeftIcon = "<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke ql-fill\" points=\"15 12 13 10 15 8 15 12\"/><line class=\"ql-stroke ql-fill\" x1=\"9\" x2=\"5\" y1=\"4\" y2=\"4\"/><path class=\"ql-fill\" d=\"M5,3A3,3,0,0,0,5,9H6V3H5Z\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"5\" y=\"4\"/><rect class=\"ql-fill\" height=\"11\" width=\"1\" x=\"7\" y=\"4\"/></svg>";
const formulaIcon = "<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z\"/><rect class=\"ql-fill\" height=\"1.6\" rx=\"0.8\" ry=\"0.8\" width=\"5\" x=\"5.15\" y=\"6.2\"/><path class=\"ql-fill\" d=\"M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z\"/></svg>";
const headerIcon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z\"/></svg>";
const header2Icon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>";
const header3Icon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>";
const header4Icon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z\"/></svg>";
const header5Icon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z\"/></svg>";
const header6Icon = "<svg viewBox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z\"/></svg>";
const italicIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"13\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"5\" x2=\"11\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"8\" x2=\"10\" y1=\"14\" y2=\"4\"/></svg>";
const imageIcon = "<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"10\" width=\"12\" x=\"3\" y=\"4\"/><circle class=\"ql-fill\" cx=\"6\" cy=\"7\" r=\"1\"/><polyline class=\"ql-even ql-fill\" points=\"5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12\"/></svg>";
const indentIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-fill ql-stroke\" points=\"3 7 3 11 5 9 3 7\"/></svg>";
const outdentIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-stroke\" points=\"5 7 5 11 3 9 5 7\"/></svg>";
const linkIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"11\" y1=\"7\" y2=\"11\"/><path class=\"ql-even ql-stroke\" d=\"M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z\"/><path class=\"ql-even ql-stroke\" d=\"M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z\"/></svg>";
const listBulletIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"6\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"3\" x2=\"3\" y1=\"14\" y2=\"14\"/></svg>";
const listCheckIcon = "<svg class=\"\" viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"4\" y2=\"4\"/><polyline class=\"ql-stroke\" points=\"3 4 4 5 6 3\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"14\" y2=\"14\"/><polyline class=\"ql-stroke\" points=\"3 14 4 15 6 13\"/><line class=\"ql-stroke\" x1=\"9\" x2=\"15\" y1=\"9\" y2=\"9\"/><polyline class=\"ql-stroke\" points=\"3 9 4 10 6 8\"/></svg>";
const listOrderedIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"4\" y2=\"4\"/><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"9\" y2=\"9\"/><line class=\"ql-stroke\" x1=\"7\" x2=\"15\" y1=\"14\" y2=\"14\"/><line class=\"ql-stroke ql-thin\" x1=\"2.5\" x2=\"4.5\" y1=\"5.5\" y2=\"5.5\"/><path class=\"ql-fill\" d=\"M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z\"/><path class=\"ql-stroke ql-thin\" d=\"M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156\"/><path class=\"ql-stroke ql-thin\" d=\"M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109\"/></svg>";
const subscriptIcon = "<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z\"/><path class=\"ql-fill\" d=\"M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z\"/></svg>";
const superscriptIcon = "<svg viewbox=\"0 0 18 18\"><path class=\"ql-fill\" d=\"M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z\"/><path class=\"ql-fill\" d=\"M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z\"/></svg>";
const strikeIcon = "<svg viewbox=\"0 0 18 18\"><line class=\"ql-stroke ql-thin\" x1=\"15.5\" x2=\"2.5\" y1=\"8.5\" y2=\"9.5\"/><path class=\"ql-fill\" d=\"M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z\"/><path class=\"ql-fill\" d=\"M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z\"/></svg>";
const tableIcon = "<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"12\" width=\"12\" x=\"3\" y=\"3\"/><rect class=\"ql-fill\" height=\"2\" width=\"3\" x=\"5\" y=\"5\"/><rect class=\"ql-fill\" height=\"2\" width=\"4\" x=\"9\" y=\"5\"/><g class=\"ql-fill ql-transparent\"><rect height=\"2\" width=\"3\" x=\"5\" y=\"8\"/><rect height=\"2\" width=\"4\" x=\"9\" y=\"8\"/><rect height=\"2\" width=\"3\" x=\"5\" y=\"11\"/><rect height=\"2\" width=\"4\" x=\"9\" y=\"11\"/></g></svg>";
const underlineIcon = "<svg viewbox=\"0 0 18 18\"><path class=\"ql-stroke\" d=\"M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3\"/><rect class=\"ql-fill\" height=\"1\" rx=\"0.5\" ry=\"0.5\" width=\"12\" x=\"3\" y=\"15\"/></svg>";
const videoIcon = "<svg viewbox=\"0 0 18 18\"><rect class=\"ql-stroke\" height=\"12\" width=\"12\" x=\"3\" y=\"3\"/><rect class=\"ql-fill\" height=\"12\" width=\"1\" x=\"5\" y=\"3\"/><rect class=\"ql-fill\" height=\"12\" width=\"1\" x=\"12\" y=\"3\"/><rect class=\"ql-fill\" height=\"2\" width=\"8\" x=\"5\" y=\"8\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"5\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"7\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"10\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"3\" y=\"12\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"5\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"7\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"10\"/><rect class=\"ql-fill\" height=\"1\" width=\"3\" x=\"12\" y=\"12\"/></svg>";
const __TURBOPACK__default__export__ = {
    align: {
        '': alignLeftIcon,
        center: alignCenterIcon,
        right: alignRightIcon,
        justify: alignJustifyIcon
    },
    background: backgroundIcon,
    blockquote: blockquoteIcon,
    bold: boldIcon,
    clean: cleanIcon,
    code: codeIcon,
    'code-block': codeIcon,
    color: colorIcon,
    direction: {
        '': directionLeftToRightIcon,
        rtl: directionRightToLeftIcon
    },
    formula: formulaIcon,
    header: {
        '1': headerIcon,
        '2': header2Icon,
        '3': header3Icon,
        '4': header4Icon,
        '5': header5Icon,
        '6': header6Icon
    },
    italic: italicIcon,
    image: imageIcon,
    indent: {
        '+1': indentIcon,
        '-1': outdentIcon
    },
    link: linkIcon,
    list: {
        bullet: listBulletIcon,
        check: listCheckIcon,
        ordered: listOrderedIcon
    },
    script: {
        sub: subscriptIcon,
        super: superscriptIcon
    },
    strike: strikeIcon,
    table: tableIcon,
    underline: underlineIcon,
    video: videoIcon
};
 //# sourceMappingURL=icons.js.map
}}),
"[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const DropdownIcon = "<svg viewbox=\"0 0 18 18\"><polygon class=\"ql-stroke\" points=\"7 11 9 13 11 11 7 11\"/><polygon class=\"ql-stroke\" points=\"7 7 9 5 11 7 7 7\"/></svg>";
let optionsCounter = 0;
function toggleAriaAttribute(element, attribute) {
    element.setAttribute(attribute, `${!(element.getAttribute(attribute) === 'true')}`);
}
class Picker {
    constructor(select){
        this.select = select;
        this.container = document.createElement('span');
        this.buildPicker();
        this.select.style.display = 'none';
        // @ts-expect-error Fix me later
        this.select.parentNode.insertBefore(this.container, this.select);
        this.label.addEventListener('mousedown', ()=>{
            this.togglePicker();
        });
        this.label.addEventListener('keydown', (event)=>{
            switch(event.key){
                case 'Enter':
                    this.togglePicker();
                    break;
                case 'Escape':
                    this.escape();
                    event.preventDefault();
                    break;
                default:
            }
        });
        this.select.addEventListener('change', this.update.bind(this));
    }
    togglePicker() {
        this.container.classList.toggle('ql-expanded');
        // Toggle aria-expanded and aria-hidden to make the picker accessible
        toggleAriaAttribute(this.label, 'aria-expanded');
        // @ts-expect-error
        toggleAriaAttribute(this.options, 'aria-hidden');
    }
    buildItem(option) {
        const item = document.createElement('span');
        // @ts-expect-error
        item.tabIndex = '0';
        item.setAttribute('role', 'button');
        item.classList.add('ql-picker-item');
        const value = option.getAttribute('value');
        if (value) {
            item.setAttribute('data-value', value);
        }
        if (option.textContent) {
            item.setAttribute('data-label', option.textContent);
        }
        item.addEventListener('click', ()=>{
            this.selectItem(item, true);
        });
        item.addEventListener('keydown', (event)=>{
            switch(event.key){
                case 'Enter':
                    this.selectItem(item, true);
                    event.preventDefault();
                    break;
                case 'Escape':
                    this.escape();
                    event.preventDefault();
                    break;
                default:
            }
        });
        return item;
    }
    buildLabel() {
        const label = document.createElement('span');
        label.classList.add('ql-picker-label');
        label.innerHTML = DropdownIcon;
        // @ts-expect-error
        label.tabIndex = '0';
        label.setAttribute('role', 'button');
        label.setAttribute('aria-expanded', 'false');
        this.container.appendChild(label);
        return label;
    }
    buildOptions() {
        const options = document.createElement('span');
        options.classList.add('ql-picker-options');
        // Don't want screen readers to read this until options are visible
        options.setAttribute('aria-hidden', 'true');
        // @ts-expect-error
        options.tabIndex = '-1';
        // Need a unique id for aria-controls
        options.id = `ql-picker-options-${optionsCounter}`;
        optionsCounter += 1;
        this.label.setAttribute('aria-controls', options.id);
        // @ts-expect-error
        this.options = options;
        Array.from(this.select.options).forEach((option)=>{
            const item = this.buildItem(option);
            options.appendChild(item);
            if (option.selected === true) {
                this.selectItem(item);
            }
        });
        this.container.appendChild(options);
    }
    buildPicker() {
        Array.from(this.select.attributes).forEach((item)=>{
            this.container.setAttribute(item.name, item.value);
        });
        this.container.classList.add('ql-picker');
        this.label = this.buildLabel();
        this.buildOptions();
    }
    escape() {
        // Close menu and return focus to trigger label
        this.close();
        // Need setTimeout for accessibility to ensure that the browser executes
        // focus on the next process thread and after any DOM content changes
        setTimeout(()=>this.label.focus(), 1);
    }
    close() {
        this.container.classList.remove('ql-expanded');
        this.label.setAttribute('aria-expanded', 'false');
        // @ts-expect-error
        this.options.setAttribute('aria-hidden', 'true');
    }
    selectItem(item) {
        let trigger = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        const selected = this.container.querySelector('.ql-selected');
        if (item === selected) return;
        if (selected != null) {
            selected.classList.remove('ql-selected');
        }
        if (item == null) return;
        item.classList.add('ql-selected');
        // @ts-expect-error Fix me later
        this.select.selectedIndex = Array.from(item.parentNode.children).indexOf(item);
        if (item.hasAttribute('data-value')) {
            // @ts-expect-error Fix me later
            this.label.setAttribute('data-value', item.getAttribute('data-value'));
        } else {
            this.label.removeAttribute('data-value');
        }
        if (item.hasAttribute('data-label')) {
            // @ts-expect-error Fix me later
            this.label.setAttribute('data-label', item.getAttribute('data-label'));
        } else {
            this.label.removeAttribute('data-label');
        }
        if (trigger) {
            this.select.dispatchEvent(new Event('change'));
            this.close();
        }
    }
    update() {
        let option;
        if (this.select.selectedIndex > -1) {
            const item = // @ts-expect-error Fix me later
            this.container.querySelector('.ql-picker-options').children[this.select.selectedIndex];
            option = this.select.options[this.select.selectedIndex];
            // @ts-expect-error
            this.selectItem(item);
        } else {
            this.selectItem(null);
        }
        const isActive = option != null && option !== this.select.querySelector('option[selected]');
        this.label.classList.toggle('ql-active', isActive);
    }
}
const __TURBOPACK__default__export__ = Picker;
 //# sourceMappingURL=picker.js.map
}}),
"[project]/node_modules/quill/ui/color-picker.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)");
;
class ColorPicker extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(select, label){
        super(select);
        this.label.innerHTML = label;
        this.container.classList.add('ql-color-picker');
        Array.from(this.container.querySelectorAll('.ql-picker-item')).slice(0, 7).forEach((item)=>{
            item.classList.add('ql-primary');
        });
    }
    buildItem(option) {
        const item = super.buildItem(option);
        item.style.backgroundColor = option.getAttribute('value') || '';
        return item;
    }
    selectItem(item, trigger) {
        super.selectItem(item, trigger);
        const colorLabel = this.label.querySelector('.ql-color-label');
        const value = item ? item.getAttribute('data-value') || '' : '';
        if (colorLabel) {
            if (colorLabel.tagName === 'line') {
                colorLabel.style.stroke = value;
            } else {
                colorLabel.style.fill = value;
            }
        }
    }
}
const __TURBOPACK__default__export__ = ColorPicker;
 //# sourceMappingURL=color-picker.js.map
}}),
"[project]/node_modules/quill/ui/icon-picker.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)");
;
class IconPicker extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(select, icons){
        super(select);
        this.container.classList.add('ql-icon-picker');
        Array.from(this.container.querySelectorAll('.ql-picker-item')).forEach((item)=>{
            item.innerHTML = icons[item.getAttribute('data-value') || ''];
        });
        this.defaultItem = this.container.querySelector('.ql-selected');
        this.selectItem(this.defaultItem);
    }
    selectItem(target, trigger) {
        super.selectItem(target, trigger);
        const item = target || this.defaultItem;
        if (item != null) {
            if (this.label.innerHTML === item.innerHTML) return;
            this.label.innerHTML = item.innerHTML;
        }
    }
}
const __TURBOPACK__default__export__ = IconPicker;
 //# sourceMappingURL=icon-picker.js.map
}}),
"[project]/node_modules/quill/ui/tooltip.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const isScrollable = (el)=>{
    const { overflowY } = getComputedStyle(el, null);
    return overflowY !== 'visible' && overflowY !== 'clip';
};
class Tooltip {
    constructor(quill, boundsContainer){
        this.quill = quill;
        this.boundsContainer = boundsContainer || document.body;
        this.root = quill.addContainer('ql-tooltip');
        // @ts-expect-error
        this.root.innerHTML = this.constructor.TEMPLATE;
        if (isScrollable(this.quill.root)) {
            this.quill.root.addEventListener('scroll', ()=>{
                this.root.style.marginTop = `${-1 * this.quill.root.scrollTop}px`;
            });
        }
        this.hide();
    }
    hide() {
        this.root.classList.add('ql-hidden');
    }
    position(reference) {
        const left = reference.left + reference.width / 2 - this.root.offsetWidth / 2;
        // root.scrollTop should be 0 if scrollContainer !== root
        const top = reference.bottom + this.quill.root.scrollTop;
        this.root.style.left = `${left}px`;
        this.root.style.top = `${top}px`;
        this.root.classList.remove('ql-flip');
        const containerBounds = this.boundsContainer.getBoundingClientRect();
        const rootBounds = this.root.getBoundingClientRect();
        let shift = 0;
        if (rootBounds.right > containerBounds.right) {
            shift = containerBounds.right - rootBounds.right;
            this.root.style.left = `${left + shift}px`;
        }
        if (rootBounds.left < containerBounds.left) {
            shift = containerBounds.left - rootBounds.left;
            this.root.style.left = `${left + shift}px`;
        }
        if (rootBounds.bottom > containerBounds.bottom) {
            const height = rootBounds.bottom - rootBounds.top;
            const verticalShift = reference.bottom - reference.top + height;
            this.root.style.top = `${top - verticalShift}px`;
            this.root.classList.add('ql-flip');
        }
        return shift;
    }
    show() {
        this.root.classList.remove('ql-editing');
        this.root.classList.remove('ql-hidden');
    }
}
const __TURBOPACK__default__export__ = Tooltip;
 //# sourceMappingURL=tooltip.js.map
}}),
"[project]/node_modules/quill/themes/base.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseTooltip": (()=>BaseTooltip),
    "default": (()=>BaseTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/merge.js [app-ssr] (ecmascript) <export default as merge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/theme.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$color$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/color-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icon$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icon-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/tooltip.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const ALIGNS = [
    false,
    'center',
    'right',
    'justify'
];
const COLORS = [
    '#000000',
    '#e60000',
    '#ff9900',
    '#ffff00',
    '#008a00',
    '#0066cc',
    '#9933ff',
    '#ffffff',
    '#facccc',
    '#ffebcc',
    '#ffffcc',
    '#cce8cc',
    '#cce0f5',
    '#ebd6ff',
    '#bbbbbb',
    '#f06666',
    '#ffc266',
    '#ffff66',
    '#66b966',
    '#66a3e0',
    '#c285ff',
    '#888888',
    '#a10000',
    '#b26b00',
    '#b2b200',
    '#006100',
    '#0047b2',
    '#6b24b2',
    '#444444',
    '#5c0000',
    '#663d00',
    '#666600',
    '#003700',
    '#002966',
    '#3d1466'
];
const FONTS = [
    false,
    'serif',
    'monospace'
];
const HEADERS = [
    '1',
    '2',
    '3',
    false
];
const SIZES = [
    'small',
    false,
    'large',
    'huge'
];
class BaseTheme extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        super(quill, options);
        const listener = (e)=>{
            if (!document.body.contains(quill.root)) {
                document.body.removeEventListener('click', listener);
                return;
            }
            if (this.tooltip != null && // @ts-expect-error
            !this.tooltip.root.contains(e.target) && // @ts-expect-error
            document.activeElement !== this.tooltip.textbox && !this.quill.hasFocus()) {
                this.tooltip.hide();
            }
            if (this.pickers != null) {
                this.pickers.forEach((picker)=>{
                    // @ts-expect-error
                    if (!picker.container.contains(e.target)) {
                        picker.close();
                    }
                });
            }
        };
        quill.emitter.listenDOM('click', document.body, listener);
    }
    addModule(name) {
        const module = super.addModule(name);
        if (name === 'toolbar') {
            // @ts-expect-error
            this.extendToolbar(module);
        }
        return module;
    }
    buildButtons(buttons, icons) {
        Array.from(buttons).forEach((button)=>{
            const className = button.getAttribute('class') || '';
            className.split(/\s+/).forEach((name)=>{
                if (!name.startsWith('ql-')) return;
                name = name.slice('ql-'.length);
                if (icons[name] == null) return;
                if (name === 'direction') {
                    // @ts-expect-error
                    button.innerHTML = icons[name][''] + icons[name].rtl;
                } else if (typeof icons[name] === 'string') {
                    // @ts-expect-error
                    button.innerHTML = icons[name];
                } else {
                    // @ts-expect-error
                    const value = button.value || '';
                    // @ts-expect-error
                    if (value != null && icons[name][value]) {
                        // @ts-expect-error
                        button.innerHTML = icons[name][value];
                    }
                }
            });
        });
    }
    buildPickers(selects, icons) {
        this.pickers = Array.from(selects).map((select)=>{
            if (select.classList.contains('ql-align')) {
                if (select.querySelector('option') == null) {
                    fillSelect(select, ALIGNS);
                }
                if (typeof icons.align === 'object') {
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icon$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](select, icons.align);
                }
            }
            if (select.classList.contains('ql-background') || select.classList.contains('ql-color')) {
                const format = select.classList.contains('ql-background') ? 'background' : 'color';
                if (select.querySelector('option') == null) {
                    fillSelect(select, COLORS, format === 'background' ? '#ffffff' : '#000000');
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$color$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](select, icons[format]);
            }
            if (select.querySelector('option') == null) {
                if (select.classList.contains('ql-font')) {
                    fillSelect(select, FONTS);
                } else if (select.classList.contains('ql-header')) {
                    fillSelect(select, HEADERS);
                } else if (select.classList.contains('ql-size')) {
                    fillSelect(select, SIZES);
                }
            }
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](select);
        });
        const update = ()=>{
            this.pickers.forEach((picker)=>{
                picker.update();
            });
        };
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.EDITOR_CHANGE, update);
    }
}
BaseTheme.DEFAULTS = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$theme$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DEFAULTS, {
    modules: {
        toolbar: {
            handlers: {
                formula () {
                    this.quill.theme.tooltip.edit('formula');
                },
                image () {
                    let fileInput = this.container.querySelector('input.ql-image[type=file]');
                    if (fileInput == null) {
                        fileInput = document.createElement('input');
                        fileInput.setAttribute('type', 'file');
                        fileInput.setAttribute('accept', this.quill.uploader.options.mimetypes.join(', '));
                        fileInput.classList.add('ql-image');
                        fileInput.addEventListener('change', ()=>{
                            const range = this.quill.getSelection(true);
                            this.quill.uploader.upload(range, fileInput.files);
                            fileInput.value = '';
                        });
                        this.container.appendChild(fileInput);
                    }
                    fileInput.click();
                },
                video () {
                    this.quill.theme.tooltip.edit('video');
                }
            }
        }
    }
});
class BaseTooltip extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, boundsContainer){
        super(quill, boundsContainer);
        this.textbox = this.root.querySelector('input[type="text"]');
        this.listen();
    }
    listen() {
        // @ts-expect-error Fix me later
        this.textbox.addEventListener('keydown', (event)=>{
            if (event.key === 'Enter') {
                this.save();
                event.preventDefault();
            } else if (event.key === 'Escape') {
                this.cancel();
                event.preventDefault();
            }
        });
    }
    cancel() {
        this.hide();
        this.restoreFocus();
    }
    edit() {
        let mode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'link';
        let preview = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
        this.root.classList.remove('ql-hidden');
        this.root.classList.add('ql-editing');
        if (this.textbox == null) return;
        if (preview != null) {
            this.textbox.value = preview;
        } else if (mode !== this.root.getAttribute('data-mode')) {
            this.textbox.value = '';
        }
        const bounds = this.quill.getBounds(this.quill.selection.savedRange);
        if (bounds != null) {
            this.position(bounds);
        }
        this.textbox.select();
        this.textbox.setAttribute('placeholder', this.textbox.getAttribute(`data-${mode}`) || '');
        this.root.setAttribute('data-mode', mode);
    }
    restoreFocus() {
        this.quill.focus({
            preventScroll: true
        });
    }
    save() {
        // @ts-expect-error Fix me later
        let { value } = this.textbox;
        switch(this.root.getAttribute('data-mode')){
            case 'link':
                {
                    const { scrollTop } = this.quill.root;
                    if (this.linkRange) {
                        this.quill.formatText(this.linkRange, 'link', value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                        delete this.linkRange;
                    } else {
                        this.restoreFocus();
                        this.quill.format('link', value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                    }
                    this.quill.root.scrollTop = scrollTop;
                    break;
                }
            case 'video':
                {
                    value = extractVideoUrl(value);
                }
            // eslint-disable-next-line no-fallthrough
            case 'formula':
                {
                    if (!value) break;
                    const range = this.quill.getSelection(true);
                    if (range != null) {
                        const index = range.index + range.length;
                        this.quill.insertEmbed(index, // @ts-expect-error Fix me later
                        this.root.getAttribute('data-mode'), value, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                        if (this.root.getAttribute('data-mode') === 'formula') {
                            this.quill.insertText(index + 1, ' ', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                        }
                        this.quill.setSelection(index + 2, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                    }
                    break;
                }
            default:
        }
        // @ts-expect-error Fix me later
        this.textbox.value = '';
        this.hide();
    }
}
function extractVideoUrl(url) {
    let match = url.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/) || url.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);
    if (match) {
        return `${match[1] || 'https'}://www.youtube.com/embed/${match[2]}?showinfo=0`;
    }
    // eslint-disable-next-line no-cond-assign
    if (match = url.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/)) {
        return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;
    }
    return url;
}
function fillSelect(select, values) {
    let defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    values.forEach((value)=>{
        const option = document.createElement('option');
        if (value === defaultValue) {
            option.setAttribute('selected', 'selected');
        } else {
            option.setAttribute('value', String(value));
        }
        select.appendChild(option);
    });
}
;
 //# sourceMappingURL=base.js.map
}}),
"[project]/node_modules/quill/themes/bubble.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BubbleTooltip": (()=>BubbleTooltip),
    "default": (()=>BubbleTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/merge.js [app-ssr] (ecmascript) <export default as merge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/base.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icons.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
const TOOLBAR_CONFIG = [
    [
        'bold',
        'italic',
        'link'
    ],
    [
        {
            header: 1
        },
        {
            header: 2
        },
        'blockquote'
    ]
];
class BubbleTooltip extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseTooltip"] {
    static TEMPLATE = [
        '<span class="ql-tooltip-arrow"></span>',
        '<div class="ql-tooltip-editor">',
        '<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">',
        '<a class="ql-close"></a>',
        '</div>'
    ].join('');
    constructor(quill, bounds){
        super(quill, bounds);
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.EDITOR_CHANGE, (type, range, oldRange, source)=>{
            if (type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SELECTION_CHANGE) return;
            if (range != null && range.length > 0 && source === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER) {
                this.show();
                // Lock our width so we will expand beyond our offsetParent boundaries
                this.root.style.left = '0px';
                this.root.style.width = '';
                this.root.style.width = `${this.root.offsetWidth}px`;
                const lines = this.quill.getLines(range.index, range.length);
                if (lines.length === 1) {
                    const bounds = this.quill.getBounds(range);
                    if (bounds != null) {
                        this.position(bounds);
                    }
                } else {
                    const lastLine = lines[lines.length - 1];
                    const index = this.quill.getIndex(lastLine);
                    const length = Math.min(lastLine.length() - 1, range.index + range.length - index);
                    const indexBounds = this.quill.getBounds(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](index, length));
                    if (indexBounds != null) {
                        this.position(indexBounds);
                    }
                }
            } else if (document.activeElement !== this.textbox && this.quill.hasFocus()) {
                this.hide();
            }
        });
    }
    listen() {
        super.listen();
        // @ts-expect-error Fix me later
        this.root.querySelector('.ql-close').addEventListener('click', ()=>{
            this.root.classList.remove('ql-editing');
        });
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SCROLL_OPTIMIZE, ()=>{
            // Let selection be restored by toolbar handlers before repositioning
            setTimeout(()=>{
                if (this.root.classList.contains('ql-hidden')) return;
                const range = this.quill.getSelection();
                if (range != null) {
                    const bounds = this.quill.getBounds(range);
                    if (bounds != null) {
                        this.position(bounds);
                    }
                }
            }, 1);
        });
    }
    cancel() {
        this.show();
    }
    position(reference) {
        const shift = super.position(reference);
        const arrow = this.root.querySelector('.ql-tooltip-arrow');
        // @ts-expect-error
        arrow.style.marginLeft = '';
        if (shift !== 0) {
            // @ts-expect-error
            arrow.style.marginLeft = `${-1 * shift - arrow.offsetWidth / 2}px`;
        }
        return shift;
    }
}
class BubbleTheme extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        if (options.modules.toolbar != null && options.modules.toolbar.container == null) {
            options.modules.toolbar.container = TOOLBAR_CONFIG;
        }
        super(quill, options);
        this.quill.container.classList.add('ql-bubble');
    }
    extendToolbar(toolbar) {
        // @ts-expect-error
        this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);
        if (toolbar.container != null) {
            this.tooltip.root.appendChild(toolbar.container);
            this.buildButtons(toolbar.container.querySelectorAll('button'), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
            this.buildPickers(toolbar.container.querySelectorAll('select'), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
        }
    }
}
BubbleTheme.DEFAULTS = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DEFAULTS, {
    modules: {
        toolbar: {
            handlers: {
                link (value) {
                    if (!value) {
                        this.quill.format('link', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    } else {
                        // @ts-expect-error
                        this.quill.theme.tooltip.edit();
                    }
                }
            }
        }
    }
});
;
 //# sourceMappingURL=bubble.js.map
}}),
"[project]/node_modules/quill/themes/snow.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__ = __turbopack_context__.i("[project]/node_modules/lodash-es/merge.js [app-ssr] (ecmascript) <export default as merge>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/emitter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/base.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/core/selection.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icons.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core/quill.js [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
const TOOLBAR_CONFIG = [
    [
        {
            header: [
                '1',
                '2',
                '3',
                false
            ]
        }
    ],
    [
        'bold',
        'italic',
        'underline',
        'link'
    ],
    [
        {
            list: 'ordered'
        },
        {
            list: 'bullet'
        }
    ],
    [
        'clean'
    ]
];
class SnowTooltip extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseTooltip"] {
    static TEMPLATE = [
        '<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>',
        '<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">',
        '<a class="ql-action"></a>',
        '<a class="ql-remove"></a>'
    ].join('');
    preview = this.root.querySelector('a.ql-preview');
    listen() {
        super.listen();
        // @ts-expect-error Fix me later
        this.root.querySelector('a.ql-action').addEventListener('click', (event)=>{
            if (this.root.classList.contains('ql-editing')) {
                this.save();
            } else {
                // @ts-expect-error Fix me later
                this.edit('link', this.preview.textContent);
            }
            event.preventDefault();
        });
        // @ts-expect-error Fix me later
        this.root.querySelector('a.ql-remove').addEventListener('click', (event)=>{
            if (this.linkRange != null) {
                const range = this.linkRange;
                this.restoreFocus();
                this.quill.formatText(range, 'link', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER);
                delete this.linkRange;
            }
            event.preventDefault();
            this.hide();
        });
        this.quill.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events.SELECTION_CHANGE, (range, oldRange, source)=>{
            if (range == null) return;
            if (range.length === 0 && source === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$emitter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sources.USER) {
                const [link, offset] = this.quill.scroll.descendant(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], range.index);
                if (link != null) {
                    this.linkRange = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$selection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](range.index - offset, link.length());
                    const preview = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].formats(link.domNode);
                    // @ts-expect-error Fix me later
                    this.preview.textContent = preview;
                    // @ts-expect-error Fix me later
                    this.preview.setAttribute('href', preview);
                    this.show();
                    const bounds = this.quill.getBounds(this.linkRange);
                    if (bounds != null) {
                        this.position(bounds);
                    }
                    return;
                }
            } else {
                delete this.linkRange;
            }
            this.hide();
        });
    }
    show() {
        super.show();
        this.root.removeAttribute('data-mode');
    }
}
class SnowTheme extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] {
    constructor(quill, options){
        if (options.modules.toolbar != null && options.modules.toolbar.container == null) {
            options.modules.toolbar.container = TOOLBAR_CONFIG;
        }
        super(quill, options);
        this.quill.container.classList.add('ql-snow');
    }
    extendToolbar(toolbar) {
        if (toolbar.container != null) {
            toolbar.container.classList.add('ql-snow');
            this.buildButtons(toolbar.container.querySelectorAll('button'), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
            this.buildPickers(toolbar.container.querySelectorAll('select'), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
            // @ts-expect-error
            this.tooltip = new SnowTooltip(this.quill, this.options.bounds);
            if (toolbar.container.querySelector('.ql-link')) {
                this.quill.keyboard.addBinding({
                    key: 'k',
                    shortKey: true
                }, (_range, context)=>{
                    toolbar.handlers.link.call(toolbar, !context.format.link);
                });
            }
        }
    }
}
SnowTheme.DEFAULTS = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2d$es$2f$merge$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__merge$3e$__["merge"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DEFAULTS, {
    modules: {
        toolbar: {
            handlers: {
                link (value) {
                    if (value) {
                        const range = this.quill.getSelection();
                        if (range == null || range.length === 0) return;
                        let preview = this.quill.getText(range);
                        if (/^\S+@\S+\.\S+$/.test(preview) && preview.indexOf('mailto:') !== 0) {
                            preview = `mailto:${preview}`;
                        }
                        // @ts-expect-error
                        const { tooltip } = this.quill.theme;
                        tooltip.edit('link', preview);
                    } else {
                        this.quill.format('link', false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].sources.USER);
                    }
                }
            }
        }
    }
});
const __TURBOPACK__default__export__ = SnowTheme;
 //# sourceMappingURL=snow.js.map
}}),
"[project]/node_modules/quill/quill.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/align.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/direction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$indent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/indent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$blockquote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/blockquote.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$header$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/header.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/list.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/background.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/font.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/bold.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$italic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/italic.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/script.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$strike$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/strike.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$underline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/underline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$formula$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/formula.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/video.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$syntax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/syntax.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/table.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$toolbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/toolbar.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icons.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$color$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/color-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icon$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icon-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/tooltip.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$bubble$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/bubble.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$snow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/snow.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register({
    'attributors/attribute/direction': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionAttribute"],
    'attributors/class/align': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlignClass"],
    'attributors/class/background': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BackgroundClass"],
    'attributors/class/color': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorClass"],
    'attributors/class/direction': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionClass"],
    'attributors/class/font': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontClass"],
    'attributors/class/size': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SizeClass"],
    'attributors/style/align': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlignStyle"],
    'attributors/style/background': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BackgroundStyle"],
    'attributors/style/color': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorStyle"],
    'attributors/style/direction': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionStyle"],
    'attributors/style/font': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontStyle"],
    'attributors/style/size': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SizeStyle"]
}, true);
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].register({
    'formats/align': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlignClass"],
    'formats/direction': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionClass"],
    'formats/indent': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$indent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/background': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BackgroundStyle"],
    'formats/color': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorStyle"],
    'formats/font': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FontClass"],
    'formats/size': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SizeClass"],
    'formats/blockquote': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$blockquote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/code-block': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/header': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$header$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/list': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/bold': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/code': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Code"],
    'formats/italic': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$italic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/link': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/script': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/strike': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$strike$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/underline': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$underline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/formula': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$formula$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/image': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'formats/video': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/syntax': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$syntax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/table': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'modules/toolbar': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$toolbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'themes/bubble': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$bubble$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'themes/snow': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$snow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'ui/icons': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'ui/picker': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'ui/icon-picker': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icon$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'ui/color-picker': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$color$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
    'ui/tooltip': __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
}, true);
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
 //# sourceMappingURL=quill.js.map
}}),
"[project]/node_modules/quill/quill.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$core$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/core.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$align$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/align.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$direction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/direction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$indent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/indent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$blockquote$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/blockquote.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$header$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/header.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$list$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/list.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$background$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/background.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$font$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/font.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$bold$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/bold.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$italic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/italic.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$script$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/script.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$strike$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/strike.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$underline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/underline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$formula$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/formula.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/video.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$formats$2f$code$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/formats/code.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$syntax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/syntax.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/table.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$modules$2f$toolbar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/modules/toolbar.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icons$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icons.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$color$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/color-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$icon$2d$picker$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/icon-picker.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$ui$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/ui/tooltip.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$bubble$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/bubble.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$themes$2f$snow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/quill/themes/snow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$quill$2f$quill$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/quill/quill.js [app-ssr] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_quill_301952ae._.js.map